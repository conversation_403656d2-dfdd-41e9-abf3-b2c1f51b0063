"""
Multi-Agent PPO (MAPPO) 算法实现
基于PPO的多智能体强化学习算法，适用于车联网边缘计算场景
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import gym

from algorithms.ppo import PPOConfig, ActorCriticNetwork, MultiDiscreteActorCritic
from config.config import SystemConfig


@dataclass
class MAPPOConfig(PPOConfig):
    """MAPPO配置类，继承PPO配置"""
    # 多智能体特有参数
    num_agents: int = 3  # 智能体数量 (RSU, UAV, Vehicle)
    centralized_critic: bool = True  # 是否使用中心化评论家
    shared_policy: bool = False  # 是否共享策略网络
    agent_specific_obs: bool = True  # 是否使用智能体特定观测

    # 协作学习参数
    value_loss_weight: float = 1.0  # 价值损失权重
    policy_loss_weight: float = 1.0  # 策略损失权重
    entropy_weight: float = 0.01  # 熵正则化权重

    # 经验共享参数
    experience_sharing: bool = True  # 是否共享经验
    shared_buffer_size: int = 10000  # 共享经验缓冲区大小


class CentralizedCritic(nn.Module):
    """中心化评论家网络"""

    def __init__(self, global_state_dim: int, hidden_dim: int = 256):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(global_state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )

    def forward(self, global_state):
        return self.network(global_state)


class MAPPOAgent:
    """MAPPO智能体"""

    def __init__(self, agent_id: str, state_dim: int, action_space: gym.Space,
                 global_state_dim: int, config: MAPPOConfig):
        self.agent_id = agent_id
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 创建演员-评论家网络（分散执行）
        if isinstance(action_space, gym.spaces.MultiDiscrete):
            self.action_dims = action_space.nvec.tolist()
            self.actor_critic = MultiDiscreteActorCritic(state_dim, self.action_dims, config.hidden_dim).to(self.device)
        else:
            self.actor_critic = ActorCriticNetwork(state_dim, action_space.n, config.hidden_dim).to(self.device)

        # 创建中心化评论家（如果启用）
        if config.centralized_critic:
            self.centralized_critic = CentralizedCritic(global_state_dim).to(self.device)
            self.critic_optimizer = optim.Adam(self.centralized_critic.parameters(), lr=config.learning_rate)

        # 优化器 - 为整个网络创建优化器
        self.actor_optimizer = optim.Adam(self.actor_critic.parameters(), lr=config.learning_rate)

        # 经验缓冲区
        self.reset_buffer()

    def reset_buffer(self):
        """重置经验缓冲区"""
        self.states = []
        self.global_states = []
        self.actions = []
        self.rewards = []
        self.log_probs = []
        self.values = []
        self.dones = []

    def get_action(self, state: np.ndarray, global_state: np.ndarray = None,
                   deterministic: bool = False) -> Tuple[np.ndarray, float, float]:
        """获取动作"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        with torch.no_grad():
            if deterministic:
                # 确定性策略
                action_probs, _ = self.actor_critic(state_tensor)
                if isinstance(action_probs, list):
                    action = torch.stack([probs.argmax(dim=-1) for probs in action_probs], dim=-1)
                else:
                    action = action_probs.argmax(dim=-1)
                log_prob = torch.zeros(1)

                if self.config.centralized_critic and global_state is not None:
                    global_state_tensor = torch.FloatTensor(global_state).unsqueeze(0).to(self.device)
                    value = self.centralized_critic(global_state_tensor)
                else:
                    _, value = self.actor_critic(state_tensor)
            else:
                # 随机策略
                action, log_prob, _, value_from_actor = self.actor_critic.get_action_and_value(state_tensor)

                if self.config.centralized_critic and global_state is not None:
                    global_state_tensor = torch.FloatTensor(global_state).unsqueeze(0).to(self.device)
                    value = self.centralized_critic(global_state_tensor)
                else:
                    value = value_from_actor

        return action.cpu().numpy(), log_prob.cpu().numpy(), value.cpu().numpy()

    def store_transition(self, state: np.ndarray, global_state: np.ndarray,
                        action: np.ndarray, reward: float, log_prob: float,
                        value: float, done: bool):
        """存储转换"""
        self.states.append(state)
        self.global_states.append(global_state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.dones.append(done)

    def compute_advantages(self, next_value: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
        """计算优势函数"""
        rewards = np.array(self.rewards)
        values = np.array(self.values)
        dones = np.array(self.dones)

        # 计算回报
        returns = np.zeros_like(rewards)
        advantages = np.zeros_like(rewards)

        # GAE计算
        gae = 0
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[t]
                next_value_t = next_value
            else:
                next_non_terminal = 1.0 - dones[t]
                next_value_t = values[t + 1]

            delta = rewards[t] + self.config.gamma * next_value_t * next_non_terminal - values[t]
            gae = delta + self.config.gamma * self.config.gae_lambda * next_non_terminal * gae
            advantages[t] = gae
            returns[t] = advantages[t] + values[t]

        return returns, advantages

    def update(self, returns: np.ndarray, advantages: np.ndarray) -> Dict[str, float]:
        """更新网络参数"""
        # 转换为张量
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        global_states = torch.FloatTensor(np.array(self.global_states)).to(self.device)
        actions = torch.FloatTensor(np.array(self.actions)).to(self.device)
        old_log_probs = torch.FloatTensor(np.array(self.log_probs)).to(self.device)
        returns_tensor = torch.FloatTensor(returns).to(self.device)
        advantages_tensor = torch.FloatTensor(advantages).to(self.device)

        # 标准化优势
        advantages_tensor = (advantages_tensor - advantages_tensor.mean()) / (advantages_tensor.std() + 1e-8)

        # 多轮更新
        total_actor_loss = 0
        total_critic_loss = 0

        for _ in range(self.config.n_epochs):
            # 使用PPO网络的get_action_and_value方法
            _, new_log_probs, entropy, current_values_from_actor = self.actor_critic.get_action_and_value(states, actions)

            # 如果使用中心化评论家，覆盖价值估计
            if self.config.centralized_critic:
                current_values = self.centralized_critic(global_states).squeeze()
            else:
                current_values = current_values_from_actor.squeeze()

            # 计算比率
            ratio = torch.exp(new_log_probs - old_log_probs)

            # PPO损失
            surr1 = ratio * advantages_tensor
            surr2 = torch.clamp(ratio, 1 - self.config.clip_epsilon, 1 + self.config.clip_epsilon) * advantages_tensor
            actor_loss = -torch.min(surr1, surr2).mean()

            # 熵损失
            entropy_loss = -entropy.mean()

            # 价值损失
            critic_loss = F.mse_loss(current_values, returns_tensor)

            # 总损失
            total_loss = (actor_loss +
                         self.config.value_loss_weight * critic_loss +
                         self.config.entropy_weight * entropy_loss)

            # 更新网络
            self.actor_optimizer.zero_grad()
            if self.config.centralized_critic:
                self.critic_optimizer.zero_grad()

            total_loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.actor_critic.parameters(), self.config.max_grad_norm)
            if self.config.centralized_critic:
                torch.nn.utils.clip_grad_norm_(self.centralized_critic.parameters(), self.config.max_grad_norm)

            self.actor_optimizer.step()
            if self.config.centralized_critic:
                self.critic_optimizer.step()

            total_actor_loss += actor_loss.item()
            total_critic_loss += critic_loss.item()

        # 清空缓冲区
        self.reset_buffer()

        return {
            'actor_loss': total_actor_loss / self.config.n_epochs,
            'critic_loss': total_critic_loss / self.config.n_epochs,
            'entropy': -entropy_loss.item()
        }

    def save(self, path: str):
        """保存模型"""
        torch.save({
            'actor_critic': self.actor_critic.state_dict(),
            'centralized_critic': self.centralized_critic.state_dict() if self.config.centralized_critic else None,
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic_optimizer': self.critic_optimizer.state_dict() if self.config.centralized_critic else None,
        }, path)

    def load(self, path: str):
        """加载模型"""
        checkpoint = torch.load(path, map_location=self.device)
        self.actor_critic.load_state_dict(checkpoint['actor_critic'])
        if self.config.centralized_critic and checkpoint['centralized_critic'] is not None:
            self.centralized_critic.load_state_dict(checkpoint['centralized_critic'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        if self.config.centralized_critic and checkpoint['critic_optimizer'] is not None:
            self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer'])


class MAPPOTrainer:
    """MAPPO训练器"""

    def __init__(self, env, agents: Dict[str, MAPPOAgent], config: MAPPOConfig):
        self.env = env
        self.agents = agents
        self.config = config

        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.training_metrics = []

    def get_global_state(self, local_states: Dict[str, np.ndarray]) -> np.ndarray:
        """构建全局状态"""
        # 简单拼接所有智能体的局部状态
        global_state = np.concatenate(list(local_states.values()))
        return global_state

    def train(self, total_timesteps: int) -> Dict:
        """训练MAPPO"""
        timestep = 0
        episode = 0

        print(f"开始MAPPO训练，总时间步数: {total_timesteps}")

        while timestep < total_timesteps:
            # 重置环境
            state = self.env.reset()
            episode_reward = {agent_id: 0 for agent_id in self.agents.keys()}
            episode_length = 0
            done = False

            # 为每个智能体重置缓冲区
            for agent in self.agents.values():
                agent.reset_buffer()

            while not done and timestep < total_timesteps:
                # 构建智能体状态字典
                agent_states = {
                    'rsu': state,  # RSU智能体观测
                    'uav': state,  # UAV智能体观测
                    'vehicle': state  # Vehicle智能体观测
                }

                # 构建全局状态
                global_state = self.get_global_state(agent_states)

                # 每个智能体选择动作
                actions = {}
                log_probs = {}
                values = {}

                for agent_id, agent in self.agents.items():
                    action, log_prob, value = agent.get_action(
                        agent_states[agent_id], global_state
                    )
                    actions[agent_id] = action
                    log_probs[agent_id] = log_prob
                    values[agent_id] = value

                # 执行联合动作（这里简化为使用第一个智能体的动作）
                joint_action = actions['rsu']  # 或者可以设计动作融合策略

                # 环境步进
                next_state, reward, done, info = self.env.step(joint_action.squeeze())

                # 为每个智能体分配奖励（这里简化为相同奖励）
                agent_rewards = {agent_id: reward for agent_id in self.agents.keys()}

                # 存储经验
                for agent_id, agent in self.agents.items():
                    agent.store_transition(
                        agent_states[agent_id], global_state, actions[agent_id],
                        agent_rewards[agent_id], log_probs[agent_id],
                        values[agent_id], done
                    )
                    episode_reward[agent_id] += agent_rewards[agent_id]

                state = next_state
                episode_length += 1
                timestep += 1

            # Episode结束，更新所有智能体
            update_metrics = {}
            for agent_id, agent in self.agents.items():
                if len(agent.rewards) > 0:
                    returns, advantages = agent.compute_advantages()
                    metrics = agent.update(returns, advantages)
                    for k, v in metrics.items():
                        update_metrics[f'{agent_id}_{k}'] = v

            # 记录统计信息
            avg_episode_reward = np.mean(list(episode_reward.values()))
            self.episode_rewards.append(avg_episode_reward)
            self.episode_lengths.append(episode_length)
            self.training_metrics.append(update_metrics)

            episode += 1

            # 打印训练进度
            if episode % 10 == 0:
                avg_reward = np.mean(self.episode_rewards[-10:])
                avg_length = np.mean(self.episode_lengths[-10:])
                print(f"MAPPO - Timestep: {timestep}, Episodes: {episode}, "
                      f"Avg Reward: {avg_reward:.2f}, Avg Length: {avg_length:.2f}")

        return {
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'training_metrics': self.training_metrics
        }
