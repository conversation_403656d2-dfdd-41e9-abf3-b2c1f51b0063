"""
队列系统
实现多优先级、多生命周期队列管理
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any

from config.config import SystemConfig, TaskConfig
from models.data_structures import Task, TaskStatus, Vehicle, RSU, UAV, QueueState

class QueueSystem:
    """
    队列系统 - 基于Paper的M/M/1优先级队列模型
    管理车辆、RSU和UAV上的多优先级、多生命周期任务队列
    """
    def __init__(self, system_config: SystemConfig, task_config: TaskConfig):
        """
        初始化队列系统
        Args:
            system_config: 系统配置
            task_config: 任务配置
        """
        self.config = {
            'system': system_config,
            'task': task_config
        }
        self.max_lifetime = task_config.max_lifetime
        self.num_priorities = task_config.num_priorities

        # M/M/1队列参数 (Paper Section 4)
        self.service_rates = {
            'vehicle': 2e9,  # 车辆服务率 (cycles/s)
            'rsu': 8e9,      # RSU服务率 (cycles/s)
            'uav': 3e9       # UAV服务率 (cycles/s)
        }

        # 优先级权重 (Paper Section 5)
        self.priority_weights = [3.0, 2.0, 1.0]  # 高、中、低优先级权重

        # 性能统计
        self.total_dropped_data = 0
        self.total_processed_data = 0
        self.queue_delay_stats = {}  # 队列延迟统计

    def initialize_vehicle_queues(self, vehicle: Vehicle) -> Dict[Tuple[int, int], QueueState]:
        """
        初始化车辆队列
        Args:
            vehicle: 车辆节点
        Returns:
            初始化的队列字典 {(lifetime, priority): QueueState}
        """
        queues = {}
        for lifetime in range(1, self.max_lifetime + 1):
            for priority in range(1, self.num_priorities + 1):
                queues[(lifetime, priority)] = QueueState(lifetime, priority)
        return queues

    def initialize_rsu_queues(self, rsu: RSU) -> Dict[Tuple[int, int], QueueState]:
        """
        初始化RSU队列
        Args:
            rsu: RSU节点
        Returns:
            初始化的队列字典 {(lifetime, priority): QueueState}
        """
        queues = {}
        for lifetime in range(1, self.max_lifetime):  # RSU使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                queues[(lifetime, priority)] = QueueState(lifetime, priority)
        return queues

    def initialize_uav_queues(self, uav: UAV) -> Dict[Tuple[int, int], QueueState]:
        """
        初始化UAV队列
        Args:
            uav: UAV节点
        Returns:
            初始化的队列字典 {(lifetime, priority): QueueState}
        """
        queues = {}
        for lifetime in range(1, self.max_lifetime):  # UAV使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                queues[(lifetime, priority)] = QueueState(lifetime, priority)
        return queues

    def update_vehicle_queues(self, vehicle: Vehicle, decisions: Dict[str, float],
                            incoming_data: Dict[str, float]) -> Dict[str, float]:
        """
        更新车辆队列状态
        Args:
            vehicle: 车辆节点
            decisions: 决策变量字典
            incoming_data: 传入数据量字典
        Returns:
            数据丢失统计
        """
        data_loss = {
            'total': 0.0,
            'by_priority': {}
        }

        # 生成新任务（简化版）
        new_tasks = []
        for priority in range(1, self.num_priorities + 1):
            if np.random.random() < 0.1:  # 10%概率生成新任务
                task = Task(
                    task_id=f"task_{vehicle.node_id}_{np.random.randint(1000, 9999)}",
                    vehicle_id=vehicle.node_id,
                    data_size=np.random.uniform(1e6, 10e6),  # 1MB - 10MB
                    computation_cycles=np.random.uniform(1e9, 10e9),  # 1G - 10G cycles
                    output_size=np.random.uniform(1e4, 1e5),  # 10KB - 100KB
                    arrival_time=0,  # 当前时间，外部设置
                    max_tolerance=np.random.randint(5, 20),  # 5-20个时隙
                    priority=priority,
                    lifetime=self.max_lifetime,  # 起始生命周期为最大值
                    status=TaskStatus.WAITING
                )
                new_tasks.append(task)

        # 创建新队列
        new_queues = {}
        for lifetime in range(1, self.max_lifetime + 1):
            for priority in range(1, self.num_priorities + 1):
                new_queues[(lifetime, priority)] = QueueState(lifetime, priority)

        # 队列状态转移（老化处理）
        for (old_lifetime, priority), queue_state in vehicle.queues.items():
            if old_lifetime == 1:
                # 生命周期为1的任务被丢弃
                data_loss['total'] += queue_state.data_amount
                data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + queue_state.data_amount
            elif old_lifetime > 1:
                # 老化处理
                new_lifetime = old_lifetime - 1
                new_queues[(new_lifetime, priority)].data_amount += queue_state.data_amount

                # 处理任务列表
                remaining_tasks = []
                for task in queue_state.task_list:
                    # 更新任务生命周期
                    task.lifetime -= 1
                    if task.lifetime > 0:
                        remaining_tasks.append(task)
                    else:
                        # 任务过期，添加到丢失统计
                        data_loss['total'] += task.data_size
                        data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + task.data_size

                new_queues[(new_lifetime, priority)].task_list = remaining_tasks

        # 添加新任务到最大生命周期队列
        for task in new_tasks:
            queue_key = (self.max_lifetime, task.priority)
            new_queues[queue_key].data_amount += task.data_size
            new_queues[queue_key].task_list.append(task)

        # 处理传入数据（从其他节点迁移过来的）
        for key, amount in incoming_data.items():
            if key.startswith(f'vehicle_{vehicle.node_id}_'):
                # 解析传入数据的生命周期和优先级
                parts = key.split('_')
                if len(parts) >= 4:
                    lifetime = int(parts[2])
                    priority = int(parts[3])
                    if (lifetime, priority) in new_queues:
                        new_queues[(lifetime, priority)].data_amount += amount

        # 处理本地计算和迁移决策
        for (old_lifetime, priority), queue_state in new_queues.items():
            # 本地处理
            local_processed = decisions.get(f'local_{old_lifetime}_{priority}', 0.0)
            queue_state.data_amount -= local_processed

            # 迁移到RSU
            for rsu_id in range(self.config['system'].num_rsus):
                migrated = decisions.get(f'migrate_rsu_{rsu_id}_{old_lifetime}_{priority}', 0.0)
                queue_state.data_amount -= migrated

            # 迁移到UAV
            for uav_id in range(self.config['system'].num_uavs):
                migrated = decisions.get(f'migrate_uav_{uav_id}_{old_lifetime}_{priority}', 0.0)
                queue_state.data_amount -= migrated

            # 确保数据量不为负
            queue_state.data_amount = max(0.0, queue_state.data_amount)

        # 更新车辆队列
        vehicle.queues = new_queues

        return data_loss

    def update_rsu_queues(self, rsu: RSU, decisions: Dict[str, float],
                         incoming_data: Dict[str, float]) -> Dict[str, float]:
        """
        更新RSU队列状态
        Args:
            rsu: RSU节点
            decisions: 决策变量字典
            incoming_data: 传入数据量字典
        Returns:
            数据丢失统计
        """
        data_loss = {
            'total': 0.0,
            'by_priority': {}
        }

        # 创建新队列
        new_queues = {}
        for lifetime in range(1, self.max_lifetime):  # RSU使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                new_queues[(lifetime, priority)] = QueueState(lifetime, priority)

        # 队列状态转移（老化处理）
        for (old_lifetime, priority), queue_state in rsu.queues.items():
            if old_lifetime == 1:
                # 生命周期为1的任务被丢弃
                data_loss['total'] += queue_state.data_amount
                data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + queue_state.data_amount
            elif old_lifetime > 1:
                # 老化处理
                new_lifetime = old_lifetime - 1
                new_queues[(new_lifetime, priority)].data_amount += queue_state.data_amount

                # 处理任务列表
                remaining_tasks = []
                for task in queue_state.task_list:
                    task.lifetime -= 1
                    if task.lifetime > 0:
                        remaining_tasks.append(task)
                    else:
                        data_loss['total'] += task.data_size
                        data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + task.data_size

                new_queues[(new_lifetime, priority)].task_list = remaining_tasks

        # 处理传入数据（从车辆迁移过来的）
        for key, amount in incoming_data.items():
            if key.startswith(f'rsu_{rsu.node_id}_'):
                parts = key.split('_')
                if len(parts) >= 4:
                    lifetime = int(parts[2])
                    priority = int(parts[3])
                    if (lifetime, priority) in new_queues:
                        new_queues[(lifetime, priority)].data_amount += amount

        # 处理本地计算决策
        for (old_lifetime, priority), queue_state in new_queues.items():
            # 本地处理
            local_processed = decisions.get(f'rsu_local_{old_lifetime}_{priority}', 0.0)
            queue_state.data_amount -= local_processed

            # 迁移到其他RSU
            for other_rsu_id in range(self.config['system'].num_rsus):
                if other_rsu_id != rsu.node_id:
                    migrated = decisions.get(f'migrate_{other_rsu_id}_{old_lifetime}_{priority}', 0.0)
                    queue_state.data_amount -= migrated

            # 确保数据量不为负
            queue_state.data_amount = max(0.0, queue_state.data_amount)

        # 更新RSU队列
        rsu.queues = new_queues

        return data_loss

    def update_uav_queues(self, uav: UAV, decisions: Dict[str, float],
                         incoming_data: Dict[str, float]) -> Dict[str, float]:
        """
        更新UAV队列状态
        Args:
            uav: UAV节点
            decisions: 决策变量字典
            incoming_data: 传入数据量字典
        Returns:
            数据丢失统计
        """
        data_loss = {
            'total': 0.0,
            'by_priority': {}
        }

        # 创建新队列
        new_queues = {}
        for lifetime in range(1, self.max_lifetime):  # UAV使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                new_queues[(lifetime, priority)] = QueueState(lifetime, priority)

        # 队列状态转移（老化处理）
        for (old_lifetime, priority), queue_state in uav.queues.items():
            if old_lifetime == 1:
                # 生命周期为1的任务被丢弃
                data_loss['total'] += queue_state.data_amount
                data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + queue_state.data_amount
            elif old_lifetime > 1:
                # 老化处理
                new_lifetime = old_lifetime - 1
                new_queues[(new_lifetime, priority)].data_amount += queue_state.data_amount

                # 处理任务列表
                remaining_tasks = []
                for task in queue_state.task_list:
                    task.lifetime -= 1
                    if task.lifetime > 0:
                        remaining_tasks.append(task)
                    else:
                        data_loss['total'] += task.data_size
                        data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + task.data_size

                new_queues[(new_lifetime, priority)].task_list = remaining_tasks

        # 处理传入数据（从车辆迁移过来的）
        for key, amount in incoming_data.items():
            if key.startswith(f'uav_{uav.node_id}_'):
                parts = key.split('_')
                if len(parts) >= 4:
                    lifetime = int(parts[2])
                    priority = int(parts[3])
                    if (lifetime, priority) in new_queues:
                        new_queues[(lifetime, priority)].data_amount += amount

        # 处理本地计算决策
        for (old_lifetime, priority), queue_state in new_queues.items():
            # 本地处理
            local_processed = decisions.get(f'uav_local_{old_lifetime}_{priority}', 0.0)
            queue_state.data_amount -= local_processed

            # 确保数据量不为负
            queue_state.data_amount = max(0.0, queue_state.data_amount)

        # 更新UAV队列
        uav.queues = new_queues

        return data_loss

    def get_queue_statistics(self, queues: Dict[Tuple[int, int], QueueState]) -> Dict[str, float]:
        """
        获取队列统计信息
        Args:
            queues: 队列字典
        Returns:
            统计信息字典
        """
        total_data = 0.0
        total_tasks = 0
        priority_data = {}
        lifetime_data = {}

        for (lifetime, priority), queue_state in queues.items():
            total_data += queue_state.data_amount
            total_tasks += len(queue_state.task_list)

            # 按优先级统计
            if priority not in priority_data:
                priority_data[priority] = 0.0
            priority_data[priority] += queue_state.data_amount

            # 按生命周期统计
            if lifetime not in lifetime_data:
                lifetime_data[lifetime] = 0.0
            lifetime_data[lifetime] += queue_state.data_amount

        return {
            'total_data': total_data,
            'total_tasks': total_tasks,
            'priority_distribution': priority_data,
            'lifetime_distribution': lifetime_data,
            'average_data_per_task': total_data / max(1, total_tasks)
        }

    def add_new_tasks(self, tasks: List[Task], current_time: int):
        """
        将新生成的任务添加到相应节点的队列
        Args:
            tasks: 新生成的任务列表
            current_time: 当前时隙
        """
        for task in tasks:
            # 设置任务到达时间
            task.arrival_time = current_time

            # 计算初始生命周期
            task.lifetime = min(task.max_tolerance, self.max_lifetime)

            # 这里可以添加将任务分配到具体节点队列的逻辑
            # 目前暂时跳过，因为这需要与具体的节点对象交互
            pass

    def calculate_mm1_queue_delay(self, node_type: str, arrival_rate: float,
                                 task_size: float, priority: int) -> float:
        """
        基于M/M/1优先级队列模型计算预期延迟 (Paper Section 4.2)
        Args:
            node_type: 节点类型 ('vehicle', 'rsu', 'uav')
            arrival_rate: 任务到达率 (tasks/s)
            task_size: 任务大小 (cycles)
            priority: 任务优先级 (1=高, 2=中, 3=低)
        Returns:
            预期队列延迟 (秒)
        """
        # 获取服务率
        mu = self.service_rates.get(node_type, 2e9)  # cycles/s

        # 计算服务时间
        service_time = task_size / mu  # 秒

        # 计算利用率
        rho = arrival_rate * service_time

        # 避免不稳定状态
        if rho >= 1.0:
            return float('inf')

        # 基础M/M/1队列延迟
        base_delay = service_time / (1 - rho)

        # 优先级调整 (Paper Section 5.1)
        priority_factor = self.priority_weights[min(priority-1, len(self.priority_weights)-1)]
        adjusted_delay = base_delay / priority_factor

        return adjusted_delay

    def estimate_queue_load(self, queues: Dict[Tuple[int, int], QueueState]) -> float:
        """
        估算队列负载 (Paper Section 4.3)
        Args:
            queues: 队列字典
        Returns:
            队列负载估计值
        """
        total_computation = 0.0
        total_tasks = 0

        for (lifetime, priority), queue_state in queues.items():
            # 估算队列中任务的计算需求
            for task in queue_state.task_list:
                if hasattr(task, 'computation_requirement'):
                    total_computation += task.computation_requirement
                else:
                    # 简化估算：数据大小 × 计算密度
                    total_computation += task.data_size * 1000  # cycles
                total_tasks += 1

        return total_computation

    def predict_completion_probability(self, task: Task, node_type: str,
                                     current_queue_load: float) -> float:
        """
        预测任务完成概率 (Paper Section 6.2)
        Args:
            task: 任务对象
            node_type: 节点类型
            current_queue_load: 当前队列负载
        Returns:
            完成概率 [0, 1]
        """
        # 计算预期处理时间
        service_rate = self.service_rates.get(node_type, 2e9)
        if hasattr(task, 'computation_requirement'):
            processing_time = task.computation_requirement / service_rate
        else:
            processing_time = (task.data_size * 1000) / service_rate

        # 考虑队列负载的延迟
        queue_delay = current_queue_load / service_rate
        total_delay = processing_time + queue_delay

        # 基于剩余生命周期计算完成概率
        remaining_time = task.lifetime * 0.1  # 假设每时隙0.1秒

        if total_delay <= remaining_time:
            # 使用指数衰减模型
            completion_prob = np.exp(-total_delay / remaining_time)
        else:
            # 超时概率很高
            completion_prob = 0.1

        # 优先级调整
        priority_bonus = self.priority_weights[min(task.priority-1, len(self.priority_weights)-1)] * 0.1
        completion_prob = min(1.0, completion_prob + priority_bonus)

        return completion_prob
