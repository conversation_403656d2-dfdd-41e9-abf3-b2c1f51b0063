"""
队列系统
实现多优先级、多生命周期队列管理
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any

from config.config import SystemConfig, TaskConfig
from models.data_structures import Task, TaskStatus, Vehicle, RSU, UAV, QueueState

class QueueSystem:
    """
    队列系统
    管理车辆、RSU和UAV上的多优先级、多生命周期任务队列
    """
    def __init__(self, system_config: SystemConfig, task_config: TaskConfig):
        """
        初始化队列系统
        Args:
            system_config: 系统配置
            task_config: 任务配置
        """
        self.config = {
            'system': system_config,
            'task': task_config
        }
        self.max_lifetime = task_config.max_lifetime
        self.num_priorities = task_config.num_priorities
        
        # 性能统计
        self.total_dropped_data = 0
        self.total_processed_data = 0
    
    def initialize_vehicle_queues(self, vehicle: Vehicle) -> Dict[Tuple[int, int], QueueState]:
        """
        初始化车辆队列
        Args:
            vehicle: 车辆节点
        Returns:
            初始化的队列字典 {(lifetime, priority): QueueState}
        """
        queues = {}
        for lifetime in range(1, self.max_lifetime + 1):
            for priority in range(1, self.num_priorities + 1):
                queues[(lifetime, priority)] = QueueState(lifetime, priority)
        return queues
    
    def initialize_rsu_queues(self, rsu: RSU) -> Dict[Tuple[int, int], QueueState]:
        """
        初始化RSU队列
        Args:
            rsu: RSU节点
        Returns:
            初始化的队列字典 {(lifetime, priority): QueueState}
        """
        queues = {}
        for lifetime in range(1, self.max_lifetime):  # RSU使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                queues[(lifetime, priority)] = QueueState(lifetime, priority)
        return queues
    
    def initialize_uav_queues(self, uav: UAV) -> Dict[Tuple[int, int], QueueState]:
        """
        初始化UAV队列
        Args:
            uav: UAV节点
        Returns:
            初始化的队列字典 {(lifetime, priority): QueueState}
        """
        queues = {}
        for lifetime in range(1, self.max_lifetime):  # UAV使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                queues[(lifetime, priority)] = QueueState(lifetime, priority)
        return queues

    def update_vehicle_queues(self, vehicle: Vehicle, decisions: Dict[str, float], 
                            incoming_data: Dict[str, float]) -> Dict[str, float]:
        """
        更新车辆队列状态
        Args:
            vehicle: 车辆节点
            decisions: 决策变量字典
            incoming_data: 传入数据量字典
        Returns:
            数据丢失统计
        """
        data_loss = {
            'total': 0.0,
            'by_priority': {}
        }
        
        # 生成新任务（简化版）
        new_tasks = []
        for priority in range(1, self.num_priorities + 1):
            if np.random.random() < 0.1:  # 10%概率生成新任务
                task = Task(
                    task_id=f"task_{vehicle.node_id}_{np.random.randint(1000, 9999)}",
                    vehicle_id=vehicle.node_id,
                    data_size=np.random.uniform(1e6, 10e6),  # 1MB - 10MB
                    computation_cycles=np.random.uniform(1e9, 10e9),  # 1G - 10G cycles
                    output_size=np.random.uniform(1e4, 1e5),  # 10KB - 100KB
                    arrival_time=0,  # 当前时间，外部设置
                    max_tolerance=np.random.randint(5, 20),  # 5-20个时隙
                    priority=priority,
                    lifetime=self.max_lifetime,  # 起始生命周期为最大值
                    status=TaskStatus.WAITING
                )
                new_tasks.append(task)
        
        # 创建新队列
        new_queues = {}
        for lifetime in range(1, self.max_lifetime + 1):
            for priority in range(1, self.num_priorities + 1):
                new_queues[(lifetime, priority)] = QueueState(lifetime, priority)
        
        # 队列状态转移（老化处理）
        for (old_lifetime, priority), queue_state in vehicle.queues.items():
            if old_lifetime == 1:
                # 生命周期为1的任务被丢弃
                data_loss['total'] += queue_state.data_amount
                data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + queue_state.data_amount
            elif old_lifetime > 1:
                # 老化处理
                new_lifetime = old_lifetime - 1
                new_queues[(new_lifetime, priority)].data_amount += queue_state.data_amount
                
                # 处理任务列表
                remaining_tasks = []
                for task in queue_state.task_list:
                    # 更新任务生命周期
                    task.lifetime -= 1
                    if task.lifetime > 0:
                        remaining_tasks.append(task)
                    else:
                        # 任务过期，添加到丢失统计
                        data_loss['total'] += task.data_size
                        data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + task.data_size
                
                new_queues[(new_lifetime, priority)].task_list = remaining_tasks
        
        # 添加新任务到最大生命周期队列
        for task in new_tasks:
            queue_key = (self.max_lifetime, task.priority)
            new_queues[queue_key].data_amount += task.data_size
            new_queues[queue_key].task_list.append(task)
        
        # 处理传入数据（从其他节点迁移过来的）
        for key, amount in incoming_data.items():
            if key.startswith(f'vehicle_{vehicle.node_id}_'):
                # 解析传入数据的生命周期和优先级
                parts = key.split('_')
                if len(parts) >= 4:
                    lifetime = int(parts[2])
                    priority = int(parts[3])
                    if (lifetime, priority) in new_queues:
                        new_queues[(lifetime, priority)].data_amount += amount
        
        # 处理本地计算和迁移决策
        for (old_lifetime, priority), queue_state in new_queues.items():
            # 本地处理
            local_processed = decisions.get(f'local_{old_lifetime}_{priority}', 0.0)
            queue_state.data_amount -= local_processed
            
            # 迁移到RSU
            for rsu_id in range(self.config['system'].num_rsus):
                migrated = decisions.get(f'migrate_rsu_{rsu_id}_{old_lifetime}_{priority}', 0.0)
                queue_state.data_amount -= migrated
            
            # 迁移到UAV
            for uav_id in range(self.config['system'].num_uavs):
                migrated = decisions.get(f'migrate_uav_{uav_id}_{old_lifetime}_{priority}', 0.0)
                queue_state.data_amount -= migrated
            
            # 确保数据量不为负
            queue_state.data_amount = max(0.0, queue_state.data_amount)
        
        # 更新车辆队列
        vehicle.queues = new_queues
        
        return data_loss

    def update_rsu_queues(self, rsu: RSU, decisions: Dict[str, float], 
                         incoming_data: Dict[str, float]) -> Dict[str, float]:
        """
        更新RSU队列状态
        Args:
            rsu: RSU节点
            decisions: 决策变量字典
            incoming_data: 传入数据量字典
        Returns:
            数据丢失统计
        """
        data_loss = {
            'total': 0.0,
            'by_priority': {}
        }
        
        # 创建新队列
        new_queues = {}
        for lifetime in range(1, self.max_lifetime):  # RSU使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                new_queues[(lifetime, priority)] = QueueState(lifetime, priority)
        
        # 队列状态转移（老化处理）
        for (old_lifetime, priority), queue_state in rsu.queues.items():
            if old_lifetime == 1:
                # 生命周期为1的任务被丢弃
                data_loss['total'] += queue_state.data_amount
                data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + queue_state.data_amount
            elif old_lifetime > 1:
                # 老化处理
                new_lifetime = old_lifetime - 1
                new_queues[(new_lifetime, priority)].data_amount += queue_state.data_amount
                
                # 处理任务列表
                remaining_tasks = []
                for task in queue_state.task_list:
                    task.lifetime -= 1
                    if task.lifetime > 0:
                        remaining_tasks.append(task)
                    else:
                        data_loss['total'] += task.data_size
                        data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + task.data_size
                
                new_queues[(new_lifetime, priority)].task_list = remaining_tasks
        
        # 处理传入数据（从车辆迁移过来的）
        for key, amount in incoming_data.items():
            if key.startswith(f'rsu_{rsu.node_id}_'):
                parts = key.split('_')
                if len(parts) >= 4:
                    lifetime = int(parts[2])
                    priority = int(parts[3])
                    if (lifetime, priority) in new_queues:
                        new_queues[(lifetime, priority)].data_amount += amount
        
        # 处理本地计算决策
        for (old_lifetime, priority), queue_state in new_queues.items():
            # 本地处理
            local_processed = decisions.get(f'rsu_local_{old_lifetime}_{priority}', 0.0)
            queue_state.data_amount -= local_processed
            
            # 迁移到其他RSU
            for other_rsu_id in range(self.config['system'].num_rsus):
                if other_rsu_id != rsu.node_id:
                    migrated = decisions.get(f'migrate_{other_rsu_id}_{old_lifetime}_{priority}', 0.0)
                    queue_state.data_amount -= migrated
            
            # 确保数据量不为负
            queue_state.data_amount = max(0.0, queue_state.data_amount)
        
        # 更新RSU队列
        rsu.queues = new_queues
        
        return data_loss

    def update_uav_queues(self, uav: UAV, decisions: Dict[str, float], 
                         incoming_data: Dict[str, float]) -> Dict[str, float]:
        """
        更新UAV队列状态
        Args:
            uav: UAV节点
            decisions: 决策变量字典
            incoming_data: 传入数据量字典
        Returns:
            数据丢失统计
        """
        data_loss = {
            'total': 0.0,
            'by_priority': {}
        }
        
        # 创建新队列
        new_queues = {}
        for lifetime in range(1, self.max_lifetime):  # UAV使用L-1个生命周期
            for priority in range(1, self.num_priorities + 1):
                new_queues[(lifetime, priority)] = QueueState(lifetime, priority)
        
        # 队列状态转移（老化处理）
        for (old_lifetime, priority), queue_state in uav.queues.items():
            if old_lifetime == 1:
                # 生命周期为1的任务被丢弃
                data_loss['total'] += queue_state.data_amount
                data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + queue_state.data_amount
            elif old_lifetime > 1:
                # 老化处理
                new_lifetime = old_lifetime - 1
                new_queues[(new_lifetime, priority)].data_amount += queue_state.data_amount
                
                # 处理任务列表
                remaining_tasks = []
                for task in queue_state.task_list:
                    task.lifetime -= 1
                    if task.lifetime > 0:
                        remaining_tasks.append(task)
                    else:
                        data_loss['total'] += task.data_size
                        data_loss['by_priority'][priority] = data_loss['by_priority'].get(priority, 0.0) + task.data_size
                
                new_queues[(new_lifetime, priority)].task_list = remaining_tasks
        
        # 处理传入数据（从车辆迁移过来的）
        for key, amount in incoming_data.items():
            if key.startswith(f'uav_{uav.node_id}_'):
                parts = key.split('_')
                if len(parts) >= 4:
                    lifetime = int(parts[2])
                    priority = int(parts[3])
                    if (lifetime, priority) in new_queues:
                        new_queues[(lifetime, priority)].data_amount += amount
        
        # 处理本地计算决策
        for (old_lifetime, priority), queue_state in new_queues.items():
            # 本地处理
            local_processed = decisions.get(f'uav_local_{old_lifetime}_{priority}', 0.0)
            queue_state.data_amount -= local_processed
            
            # 确保数据量不为负
            queue_state.data_amount = max(0.0, queue_state.data_amount)
        
        # 更新UAV队列
        uav.queues = new_queues
        
        return data_loss

    def get_queue_statistics(self, queues: Dict[Tuple[int, int], QueueState]) -> Dict[str, float]:
        """
        获取队列统计信息
        Args:
            queues: 队列字典
        Returns:
            统计信息字典
        """
        total_data = 0.0
        total_tasks = 0
        priority_data = {}
        lifetime_data = {}
        
        for (lifetime, priority), queue_state in queues.items():
            total_data += queue_state.data_amount
            total_tasks += len(queue_state.task_list)
            
            # 按优先级统计
            if priority not in priority_data:
                priority_data[priority] = 0.0
            priority_data[priority] += queue_state.data_amount
            
            # 按生命周期统计
            if lifetime not in lifetime_data:
                lifetime_data[lifetime] = 0.0
            lifetime_data[lifetime] += queue_state.data_amount
        
        return {
            'total_data': total_data,
            'total_tasks': total_tasks,
            'priority_distribution': priority_data,
            'lifetime_distribution': lifetime_data,
            'average_data_per_task': total_data / max(1, total_tasks)
        }

    def add_new_tasks(self, tasks: List[Task], current_time: int):
        """
        将新生成的任务添加到相应节点的队列
        Args:
            tasks: 新生成的任务列表
            current_time: 当前时隙
        """
        for task in tasks:
            # 设置任务到达时间
            task.arrival_time = current_time
            
            # 计算初始生命周期
            task.lifetime = min(task.max_tolerance, self.max_lifetime)
            
            # 这里可以添加将任务分配到具体节点队列的逻辑
            # 目前暂时跳过，因为这需要与具体的节点对象交互
            pass
