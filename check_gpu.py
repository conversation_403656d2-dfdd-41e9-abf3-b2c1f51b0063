"""
GPU检测和优化脚本
检查GPU使用情况并提供优化建议
"""

import torch
import numpy as np
import psutil
import os
from datetime import datetime

def check_gpu_availability():
    """检查GPU可用性"""
    print("=== GPU检测报告 ===")
    
    # 基本GPU信息
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    
    if cuda_available:
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name}")
            print(f"  显存: {gpu_memory:.1f} GB")
            
            # 检查当前显存使用
            if torch.cuda.is_available():
                torch.cuda.set_device(i)
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                cached = torch.cuda.memory_reserved(i) / 1024**3
                print(f"  已分配显存: {allocated:.2f} GB")
                print(f"  缓存显存: {cached:.2f} GB")
    else:
        print("❌ 未检测到CUDA GPU，将使用CPU训练")
        print("💡 建议: 安装CUDA版本的PyTorch以获得更好的性能")
    
    return cuda_available


def check_current_device_usage():
    """检查当前代码中的设备使用"""
    print("\n=== 代码GPU使用情况 ===")
    
    # 检查PPO算法中的设备设置
    try:
        from algorithms.ppo import PPOAgent
        from config.config import PPOConfig, SystemConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        
        # 创建测试环境和智能体
        system_config = SystemConfig()
        env = VehicularEdgeComputingEnv(system_config)
        ppo_config = PPOConfig()
        
        state_dim = env.observation_space.shape[0]
        action_space = env.action_space
        
        agent = PPOAgent(state_dim, action_space, ppo_config)
        
        print(f"✅ PPO智能体设备: {agent.device}")
        print(f"✅ 网络位置: {next(agent.network.parameters()).device}")
        
        # 测试GPU内存使用
        if torch.cuda.is_available():
            state = env.reset()
            action, _, _ = agent.get_action(state)
            
            allocated = torch.cuda.memory_allocated() / 1024**2
            print(f"✅ 推理后显存使用: {allocated:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查代码GPU使用失败: {e}")
        return False


def check_mappo_gpu_usage():
    """检查MAPPO的GPU使用"""
    print("\n=== MAPPO GPU使用情况 ===")
    
    try:
        from algorithms.simple_mappo import SimpleMAPPOAgent, SimpleMAPPOConfig
        from config.config import SystemConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        
        # 创建测试环境和智能体
        system_config = SystemConfig()
        env = VehicularEdgeComputingEnv(system_config)
        mappo_config = SimpleMAPPOConfig()
        
        state_dim = env.observation_space.shape[0]
        action_space = env.action_space
        
        agent = SimpleMAPPOAgent('test', state_dim, action_space, mappo_config)
        
        print(f"✅ MAPPO智能体设备: {agent.ppo_agent.device}")
        print(f"✅ MAPPO网络位置: {next(agent.ppo_agent.network.parameters()).device}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查MAPPO GPU使用失败: {e}")
        return False


def benchmark_gpu_vs_cpu():
    """GPU vs CPU性能基准测试"""
    print("\n=== GPU vs CPU性能测试 ===")
    
    if not torch.cuda.is_available():
        print("❌ 无GPU可用，跳过性能测试")
        return
    
    # 测试矩阵运算性能
    size = 1000
    iterations = 100
    
    # CPU测试
    print("测试CPU性能...")
    cpu_device = torch.device('cpu')
    start_time = datetime.now()
    
    for _ in range(iterations):
        a = torch.randn(size, size, device=cpu_device)
        b = torch.randn(size, size, device=cpu_device)
        c = torch.mm(a, b)
    
    cpu_time = (datetime.now() - start_time).total_seconds()
    print(f"CPU时间: {cpu_time:.2f}秒")
    
    # GPU测试
    print("测试GPU性能...")
    gpu_device = torch.device('cuda')
    torch.cuda.synchronize()
    start_time = datetime.now()
    
    for _ in range(iterations):
        a = torch.randn(size, size, device=gpu_device)
        b = torch.randn(size, size, device=gpu_device)
        c = torch.mm(a, b)
    
    torch.cuda.synchronize()
    gpu_time = (datetime.now() - start_time).total_seconds()
    print(f"GPU时间: {gpu_time:.2f}秒")
    
    speedup = cpu_time / gpu_time
    print(f"🚀 GPU加速比: {speedup:.1f}x")
    
    return speedup


def check_system_resources():
    """检查系统资源"""
    print("\n=== 系统资源检查 ===")
    
    # CPU信息
    cpu_count = psutil.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU核心数: {cpu_count}")
    print(f"CPU使用率: {cpu_percent}%")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / 1024**3:.1f} GB")
    print(f"可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"内存使用率: {memory.percent}%")
    
    # 磁盘信息
    disk = psutil.disk_usage('.')
    print(f"磁盘总空间: {disk.total / 1024**3:.1f} GB")
    print(f"磁盘可用空间: {disk.free / 1024**3:.1f} GB")


def optimize_gpu_settings():
    """GPU优化建议"""
    print("\n=== GPU优化建议 ===")
    
    if torch.cuda.is_available():
        print("✅ GPU优化建议:")
        print("  1. 确保使用CUDA版本的PyTorch")
        print("  2. 适当增加batch_size以充分利用GPU")
        print("  3. 使用混合精度训练(AMP)以节省显存")
        print("  4. 定期清理GPU缓存: torch.cuda.empty_cache()")
        
        # 检查当前PyTorch版本
        print(f"\n当前PyTorch版本: {torch.__version__}")
        print(f"CUDA版本: {torch.version.cuda}")
        
        # 显存优化建议
        if torch.cuda.is_available():
            total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            if total_memory < 4:
                print("⚠️  显存较小(<4GB)，建议:")
                print("    - 减小batch_size")
                print("    - 减小网络hidden_dim")
                print("    - 使用梯度累积")
            elif total_memory < 8:
                print("✅ 显存适中(4-8GB)，当前设置合适")
            else:
                print("🚀 显存充足(>8GB)，可以:")
                print("    - 增大batch_size到64或128")
                print("    - 增大网络hidden_dim到256或512")
                print("    - 使用更复杂的网络结构")
    else:
        print("❌ CPU训练优化建议:")
        print("  1. 安装CUDA版本的PyTorch")
        print("  2. 减小网络规模以加速训练")
        print("  3. 使用多进程训练")
        print("  4. 考虑使用云GPU服务")


def test_training_speed():
    """测试训练速度"""
    print("\n=== 训练速度测试 ===")
    
    try:
        from config.config import SystemConfig, PPOConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        from algorithms.ppo import PPOAgent
        
        # 创建测试环境
        system_config = SystemConfig()
        env = VehicularEdgeComputingEnv(system_config)
        
        # 创建智能体
        ppo_config = PPOConfig()
        ppo_config.n_epochs = 10  # 减少epoch用于测试
        
        state_dim = env.observation_space.shape[0]
        action_space = env.action_space
        agent = PPOAgent(state_dim, action_space, ppo_config)
        
        print(f"使用设备: {agent.device}")
        
        # 测试训练步骤
        state = env.reset()
        start_time = datetime.now()
        
        # 收集一些经验
        for _ in range(50):
            action, logprob, value = agent.get_action(state)
            next_state, reward, done, _ = env.step(action.squeeze())
            agent.store_transition(state, action.squeeze(), logprob.item(), 
                                 reward, value.item(), done)
            state = next_state if not done else env.reset()
        
        # 执行更新
        update_start = datetime.now()
        metrics = agent.update()
        update_time = (datetime.now() - update_start).total_seconds()
        
        total_time = (datetime.now() - start_time).total_seconds()
        
        print(f"✅ 50步收集时间: {total_time - update_time:.2f}秒")
        print(f"✅ 网络更新时间: {update_time:.2f}秒")
        print(f"✅ 总时间: {total_time:.2f}秒")
        
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**2
            print(f"✅ 显存使用: {allocated:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练速度测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 GPU检测和优化分析")
    print("=" * 50)
    
    # 检查GPU可用性
    gpu_available = check_gpu_availability()
    
    # 检查代码中的GPU使用
    check_current_device_usage()
    check_mappo_gpu_usage()
    
    # 系统资源检查
    check_system_resources()
    
    # 性能测试
    if gpu_available:
        speedup = benchmark_gpu_vs_cpu()
    
    # 训练速度测试
    test_training_speed()
    
    # 优化建议
    optimize_gpu_settings()
    
    print("\n" + "=" * 50)
    print("📋 总结:")
    
    if gpu_available:
        print("✅ 代码已正确配置GPU使用")
        print("✅ 所有神经网络都会自动使用GPU")
        print("✅ 训练将在GPU上进行，速度更快")
        print(f"✅ Epoch数量已设置为800")
    else:
        print("⚠️  代码将使用CPU训练")
        print("💡 建议安装CUDA版本的PyTorch以获得更好性能")
        print(f"✅ Epoch数量已设置为800")
    
    print("\n🚀 可以开始训练:")
    print("  - python main.py (单算法训练)")
    print("  - python compare_algorithms.py (算法对比)")


if __name__ == "__main__":
    main()
