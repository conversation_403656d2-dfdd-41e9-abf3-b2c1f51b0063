\documentclass[UTF8]{ctexart}
\usepackage{amsmath, amssymb, amsthm, bm}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{graphicx}
\usepackage{geometry} \geometry{a4paper, margin=1in}
\usepackage{setspace}
\usepackage{cases}
\usepackage{mathtools}
\usepackage{enumitem}
\usepackage{graphicx}

\newtheorem{definition}{定义}

% Define floor function if needed (already in mathtools?)
% \DeclarePairedDelimiter{\floor}{\lfloor}{\rfloor}

\begin{document}
%% ADDED: Section for Introduction placeholder
\section{引言（待补充）}
本文研究了...（概述研究背景、动机、挑战、本文贡献和结构）...
\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{image/chap03/image.png}
    \caption{场景图}
    \label{fig:example}
\end{figure}

\newpage

\section{系统模型}
\subsection{网络与任务模型}
\begin{itemize}
    \item 车辆集合 $\mathcal{V}$, RSU 集合 $\mathcal{R}$, UAV 集合 $\mathcal{U}$。
    \item 计算节点集合 $\mathcal{N} = \mathcal{V} \cup \mathcal{R} \cup \mathcal{U}$。
    \item 任务/数据集合 $\mathcal{J}$。任务 $j$ (或数据块) 属性:
        \begin{itemize}
            \item $D_j$: 数据大小 (bits)。
            \item $C_j$: 处理所需计算量 (CPU cycles)。$c = C_j / D_j$ 为处理密度 (cycles/bit)。
            \item $S_j$: 输出结果大小 (bits)。
            \item $T_{max,j}$: 最大可容忍延迟 (单位: 时隙数)。任务 $j$ 必须在生成后的 $T_{max,j}$ 个时隙内完成处理并返回结果 。即总端到端时延 $T_{total,j} \le T_{max,j} \times \Delta t$ (秒)。
            \item $\lambda_j'$: 任务 $j$ 的原始平均到达率 (tasks/时隙)。
            \item $v_j$: 生成任务 $j$ 的车辆。
        \end{itemize}
    \item $\Delta t$: 时隙持续时间 (秒)。
\end{itemize}
\subsection{节点模型}
\begin{itemize}
    %% MODIFIED: UAV position pos_u is fixed, v_u(t) removed
    \item 节点 $n \in \mathcal{N}$ 属性: $f_n$ (计算能力, cycles/秒), $P_{tx,n}$ (传输功率, Watts), $B_n^{max}$ (可用总带宽, Hz)。UAV $u \in \mathcal{U}$ 具有固定位置 $\text{pos}_u$。
    \item RSU 缓存 (RSU $r \in \mathcal{R}$):
    \begin{itemize}
        \item 缓存决策变量: $z_{j,r} \in \{0, 1\}$。$z_{j,r} = 1$ 表示 RSU $r$ 缓存了任务 $j$ 的处理结果 $S_j$，$z_{j,r} = 0$ 表示未缓存。
        \item 缓存容量: 每个 RSU $r$ 有一个最大缓存容量 $S_{cache,r}$ (bits)。约束为 $\sum_{j \in \mathcal{J}} z_{j,r} S_j \leq S_{cache,r}$。
        \item 结果请求概率预测: RSU $r$ 可以预测车辆请求任务 $j$ 结果的概率 $P_{req, j, r}(t)$。
        \[
            P_{req, j, r}(t) = \alpha_0 + \alpha_1 H_j + \alpha_2 \lambda_{v_j,req} + \alpha_3 F_{t} + \alpha_4 R_{area}
        \]
            $\alpha_0$ 模型的偏置项（baseline 请求概率）；
            $\alpha_1$~$\alpha_4$模型权重系数，用于衡量每个特征对请求概率的影响；
            $H_j$ 为任务 $j$ 的历史请求频率；
            $\lambda_{v_j,req}$ 是生成任务 $j$ 的车辆 $v_j$ 对此类结果的平均请求率或活动频率；
            $F_t$ 是表征时间因素的特征（例如，区分高峰期与低峰期的时间段索引或函数）；
            $R_{area}$ 是通过区域功能属性（如POI密度、交通事件发生率）预测任务结果在该区域的潜在复用价值或请求紧迫性的指示因子。

            \item 缓存命中率: 定义 RSU $r$ 的缓存命中率 $H_{hit,r}$：
        \[
            H_{hit,r} = \frac{\sum_{j \in \mathcal{C}_r} P_{req, j, r}(t) S_j}{\sum_{j \in \mathcal{J}_{req,r}} S_j}
        \]
        其中 $\mathcal{C}_r = \{j \in \mathcal{J} \mid z_{j,r}=1\}$ 是 RSU $r$ 上已缓存结果的任务集合，$\mathcal{J}_{req,r}$ 是 RSU $r$ 服务范围内可能被请求结果的任务集合。该定义为数据量加权的期望缓存命中率。
        (注：标准的缓存命中率通常定义为 命中请求数 / 总请求数。)
        \item 缓存收益 (时延节省): 缓存命中带来的时延节省 $\Delta T_{j,r}$ 是主要收益：
        \[\Delta T_{j,r} = T_j^{\text{no-cache}} - T_j^{\text{cache}}\]
        其中 $T_j^{\text{cache}}$ 通常仅包含结果下载时延 (可能加上一个极短的请求传输时延)。
    \end{itemize}
    \subsection{队列模型}
    结合生命周期、优先级和缓存机制，实现了任务的高效调度。每个节点（车辆、RSU、UAV）均维护一组分层队列，用于跟踪任务的剩余生命周期 (索引 $l$) 和优先级 ($p=1$ 为最高优先级, $p=P$ 为最低)。

    \begin{itemize}
        \item \textbf{车辆队列:} 每个车辆 $n \in \mathcal{V}$ 维护 $L \times P$ 个缓存队列 $Q_{n} = \{q_{n,l,p} \mid 1 \le l \le L, 1 \le p \le P\}$。
        \item \textbf{RSU 队列:} 每个 RSU $k \in \mathcal{R}$ 维护 $(L-1) \times P$ 个缓存队列 $G_k = \{g_{k,l,p} \mid 1 \le l \le L-1, 1 \le p \le P\}$。
        \item \textbf{UAV 队列:} 每个 UAV $u \in \mathcal{U}$ 维护 $(L-1) \times P$ 个缓存队列 $U_u = \{u_{u,l,p} \mid 1 \le l \le L-1, 1 \le p \le P\}$。
    \end{itemize}

    \textbf{排队假设:}
    \begin{itemize}
        \item 各个节点的任务到达过程服从泊松过程，服务时间服从指数分布。
        \item RSU和UAV的每个服务单元（逻辑上）可简化为具有 $P$ 个优先级的 \textbf{M/M/1 非抢占式优先级模型}。车辆本地处理队列也可能遵循类似模型。
        \item 系统使用\textbf{非抢占式调度策略}：任务一旦开始处理，即使有高优先级任务到达，也不中断当前任务；
        \item 节点队列容量有限，超过容量的任务被丢弃
        \item 缓存命中绕过机制：若任务 $j$ 在 RSU $r$ 缓存中命中（$z_{j,r}=1$），任务直接跳过（数据上传）、排队与计算，进入结果下载流程：
        \[
            T_{total,j,r}^{\text{cache-hit}} = T_{\text{req\_up}, j, r} + T_{\text{trans\_down}, j, r} \quad (\text{假设结果已在RSU } r \text{ 生成或缓存})
        \]
        其中 $T_{\text{req\_up}, j, r}$ 是车辆向RSU发送结果请求的传输时延（可能很小，甚至忽略）。此机制显著降低时延和系统负载。
    \end{itemize}

    \textbf{排队时延预测:}
    \begin{equation} \label{eq:T_wait_r_original}
        T_{wait, j, r}^{\text{pred}} \approx \frac{1}{\mu_r} \cdot \frac{\sum_{i=1}^{p_j} \rho_{i,r}}{(1 - \sum_{i=1}^{p_j-1} \rho_{i,r})(1 - \sum_{i=1}^{p_j} \rho_{i,r})}, \quad (\text{当RSU未缓存结果时})
    \end{equation}
    其中：
    \begin{itemize}
        \item $\lambda_{i,r}$: RSU $r$ 上优先级 $i$ 任务的平均到达率；
        \item $\mu_r = f_r/C_{avg,r}$：RSU $r$ 的平均服务速率 (假设任务平均计算量为 $C_{avg,r}$)；
        \item $\rho_{i,r} = \lambda_{i,r}/\mu_r$：优先级 $i$ 任务在 RSU $r$ 上的流量强度。
    \end{itemize}

    对于UAV $u$，任务 $j$（优先级 $p_j$）的预测等待时延：
    \begin{equation} \label{eq:T_wait_u_priority}
        T_{\text{wait},j,u}^{\text{pred}} \approx \frac{1}{\mu_u} \cdot \frac{\sum_{i=1}^{p_j} \rho_{i,u}}{(1 - \sum_{i=1}^{p_j-1} \rho_{i,u})(1 - \sum_{i=1}^{p_j} \rho_{i,u})},
    \end{equation}
    其中：
    \begin{itemize}
        \item $\lambda_{i,u}$: UAV $u$ 上优先级 $i$ 任务的平均到达率；
        \item $\mu_u = f_u / C_{avg,u}$：UAV $u$ 的平均服务速率 (假设任务平均计算量为 $C_{avg,u}$)；
        \item $\rho_{i,u} = \lambda_{i,u}/\mu_u$：优先级 $i$ 任务在 UAV $u$ 上的流量强度。
    \end{itemize}
     对于车辆 $v_j$ 本地处理，若也采用优先级队列，可类似估计 $T_{wait,j,v_j}^{\text{pred}}$。

    \textbf{状态转移:} 队列状态转移方程见第\ref{sec:state_transition_prio_revised}节，详细描述了任务在不同生命周期和优先级间的流动规则。

    \textit{提示: 在高负载情况下（$\sum \rho \to 1$）排队时延会急剧增长，实际系统采用仿真或动态调整 $\lambda, \mu$ 以精确估算。}

\subsection{决策变量与系统输入}
以下为时隙 $t$ 的主要决策变量和部分关键系统输入：
\begin{itemize}
    \item $x_{j,n}^t \in \{0, 1\}$: 任务 $j$ (在时隙 $t$ 到达或需要做出处理决策时) 是否分配给节点 $n$ 处理。(这是逐时隙的卸载/处理决策)。
    \item $D_{local,n,l,p}^t$: 车辆 $n$ 在时隙 $t$ 从队列 $(l, p)$ 中取出本地处理的数据量 (bits)。 ($n \in \mathcal{V}$)
    \item $D_{off,n,r,l,p}^t$: 车辆 $n$ 在时隙 $t$ 从队列 $(l, p)$ 中取出卸载到 RSU $r$ 的数据量 (bits)。
    \item $D_{off,n,u,l,p}^t$: 车辆 $n$ 在时隙 $t$ 从队列 $(l, p)$ 中取出卸载到 UAV $u$ 的数据量 (bits)。
    \item $\mu_{k}^t \in \{0, 1\}$: 时隙 $t$ RSU $k$ 是否激活处理单元 (若有此控制)。
    \item $D_{proc,k,l,p}^t$: RSU $k$ 在时隙 $t$ 从队列 $(l, p)$ 中取出处理的数据量 (bits)。 ($k \in \mathcal{R}$)
    \item $\nu_{k,k'}^t \in \{0, 1\}$: 时隙 $t$ RSU $k$ 是否向 RSU $k'$ 迁移数据。
    \item $D_{mig,k,k',l,p}^t$: RSU $k$ 在时隙 $t$ 从队列 $(l, p)$ 中取出迁移到 RSU $k'$ 的数据量 (bits)。
    \item $D_{proc,u,l,p}^t$: UAV $u$ 在时隙 $t$ 从队列 $(l, p)$ 中取出处理的数据量 (bits)。 ($u \in \mathcal{U}$)
    \item $b_{link}^t$: 分配给各通信链路 $link$ (如 $v \to r, v \to u, r \to k', u \to v$) 的带宽 (Hz)。
    %% REMOVED BY USER REQUEST: v_u^t is no longer a decision variable as UAVs are fixed
    % \item $v_u^t$: 时隙 $t$ UAV $u$ 的飞行速度 。
    \item $z_{j,r}^t$: RSU $r$ 在时隙 $t$ 是否决定缓存任务 $j$ 的结果。通常 $z_{j,r}$ 是较长周期的决策。
    \item (系统输入) $D_{gen,n,l,p}^t$: 车辆 $n$ 在时隙 $t$ 新生成并进入队列 $(l,p)$ 的数据量 (bits)。此为外部到达，非决策变量。
\end{itemize}
最终的任务分配结果 $x_{j,n}$ (任务 $j$ 最终在哪个节点 $n$ 处理完成) 是这一系列时隙决策的累积结果。
\newpage
% --- 新增/保留的任务分类部分 ---
\section{基于延迟容忍度的任务分类与卸载导向}

根据任务的延迟容忍度 $T_{max,j}$ 进行分类

\subsection{任务分类}
我们定义 $K=4$ 个任务类别，基于预设的延迟容忍度阈值 $\tau_1 < \tau_2 < \tau_3$ (单位: 时隙数)。
任务 $j$ 属于类别 $k$ $\text{(记作 Type(j) = k)}$ 如果其 $T_{max,j}$ 满足：
\begin{itemize}[label=\textbullet]
    \item 类别 1 (极度延迟敏感型): $T_{max,j} \leq \tau_1$。
    \item 类别 2 (延迟敏感型): $\tau_1 < T_{max,j} \leq \tau_2$。
    \item 类别 3 (中度延迟容忍型): $\tau_2 < T_{max,j} \leq \tau_3$。
    \item 类别 4 (延迟容忍型): $T_{max,j} > \tau_3$。
\end{itemize}

\subsection{分类驱动的卸载节点倾向性}
任务的类别 $Type(j)$ 决定了其优先考虑的候选计算节点集合 $\mathcal{N}_j^{cand} \subseteq \mathcal{N}$：
\begin{itemize}[label=\textbullet]
    \item 类别 1 ($T_{max,j} \leq \tau_1$): $\mathcal{N}_j^{cand} = \{v_j\}$。(仅考虑本地处理，因传输时延可能超过容忍度)。
    \item 类别 2 ($\tau_1 < T_{max,j} \leq \tau_2$): $\mathcal{N}_j^{cand} \subseteq \{v_j\} \cup \mathcal{R}_{nearby, low-latency}$。(首选本地，次选极近低延迟 RSU)。
    \item 类别 3 ($\tau_2 < T_{max,j} \leq \tau_3$): $\mathcal{N}_j^{cand} \subseteq \{v_j\} \cup \mathcal{R}_{reachable} \cup \mathcal{U}_{capable, nearby}$。(本地、可达 RSU、近距离且能力足够的 UAV)。
    \item 类别 4 ($T_{max,j} > \tau_3$): $\mathcal{N}_j^{cand} = \mathcal{N}$。(所有节点皆可考虑，侧重成本效益)。
\end{itemize}
 $\mathcal{N}_j^{cand}$ 是一个初步筛选的候选集。最终决策 $x_{j,n}$ 必须在 $\mathcal{N}_j^{cand}$ 内（或结合缓存情况进行扩展），通过后续的详细评估和优化来确定，确保严格满足 $T_{max,j}$。 $\mathcal{R}_{nearby}, \mathcal{U}_{nearby}$ 等需要根据当前网络拓扑和预计传输时延动态判断。

\newpage

\section{处理模式评估框架 (在候选集 $\mathcal{N}_j^{cand}$ 内，并考虑缓存)}

对任务 $j$ (或对应的数据块)，在其候选集 $\mathcal{N}_j^{cand}$ 内（以及对所有可达RSU检查缓存情况）评估处理模式。评估通常在每个时隙 $t$ 进行，核心是预测总完成时延 $T_{total,j}$ 并与 $T_{max,j} \times \Delta t$ 比较。

\subsection{模式一：本地计算 (评估节点 $n=v_j$)}
\textbf{前提}: $v_j \in \mathcal{N}_j^{cand}$。
\begin{itemize}
    \item 动作: 车辆 $n=v_j$ 决定使用本地 CPU 处理任务 $j$。
    \item 时延分析:
        \begin{itemize}
        \item 等待时延 ($T_{wait, j, v_j}$): 任务 $j$ 的数据在车辆 $v_j$ 的多优先级队列 $Q_{v_j}$ 中等待处理的时间。使用式 \eqref{eq:T_wait_r_original} 或 \eqref{eq:T_wait_u_priority} 的方法估计或基于当前队列积压进行瞬时估计。
        \item 计算时延 ($T_{comp, j, v_j}$): $T_{comp, j, v_j} = C_j / f_{v_j}$ 。
        \item 总时延预测: $T_{total, j, v_j}^{\text{pred}} = T_{wait, j, v_j} + T_{comp, j, v_j}$。
        \item 可行性检查: $T_{total, j, v_j}^{\text{pred}} \leq T_{max,j} \times \Delta t$
        \end{itemize}
    \item 队列交互: 若决策执行，数据 $D_j$ 从 $q_{v_j,l,p}^t$ (任务 $j$ 所在队列) 中移除。
\end{itemize}

\subsection{模式二：卸载到 RSU (评估 $r \in \mathcal{R}$，优先考虑 $\mathcal{N}_j^{cand}$ 内的RSU)}
\text{前提}: 车辆 $n=v_j$ 在 RSU $r$ 的通信范围内。
\begin{itemize}
    \item \textbf{步骤1: 检查RSU $r$ 是否缓存了任务 $j$ 的结果 ( $z_{j,r}=1$)}。
    \item \textbf{如果 $z_{j,r}=1$ (缓存命中):}
        \begin{itemize}
            \item 动作: 车辆 $n=v_j$ 从 RSU $r$ 请求已缓存的结果 $S_j$。
            \item 请求上传时延 ($T_{\text{req\_up},j,r}$): 车辆发送一个小的请求包给RSU $r$ 的时延。$T_{\text{req\_up},j,r} = D_{req} / R_{n,r}$，其中 $D_{req}$ 是请求包大小 (通常很小)。
            \item 结果下载时延 ($T_{\text{trans\_down},j,r}$): $T_{\text{trans\_down},j,r} = S_j / R_{r,n}$。
            \item 总时延预测 (缓存命中): $T_{total,j,r}^{\text{pred, cache-hit}} = T_{\text{req\_up},j,r} + T_{\text{trans\_down},j,r}$。
            \item 可行性检查: $T_{total,j,r}^{\text{pred, cache-hit}} \leq T_{max,j} \times \Delta t$。
            \item 队列交互: 任务 $j$ 不进入 RSU $r$ 的计算队列 $G_r$。原始数据 $D_j$ 无需从车辆上传到RSU。车辆队列 $q_{v_j,l,p}^t$ 中对应任务 $j$ 的数据被移除（或标记为已处理）。
        \end{itemize}
    \item \textbf{如果 $z_{j,r}=0$ (缓存未命中) 并且 $r \in \mathcal{N}_j^{cand}$ :}
        \begin{itemize}
            \item 动作: 车辆 $n=v_j$ 决定卸载原始任务数据 $D_j$ 到 RSU $r$ 进行处理。
            \item 时延分析:
                \begin{itemize}
                    \item 上传传输时延 ($T_{trans\_up, j, r}$): $T_{trans\_up, j, r} = D_j/R_{n,r}$。
                    \item 等待时延 ($T_{wait, j, r}$): 任务 $j$ 在 RSU $r$ 的多优先级队列 $G_r$ 中等待处理的时间。\\
                    \textbf{长期平均预测:} 使用式~\eqref{eq:T_wait_r_original}。\\
                                        \item 瞬时积压预测 (用于MDP状态，任务$j$优先级为$p_j$，在$t_{arr}$时刻到达RSU $r$):
                        \begin{equation} \label{eq:T_wait_r_instantaneous_pred_sec4} % Ensure unique label
                            T_{wait, j, r}^{\text{inst-pred}}(t_{arr}) \approx \frac{C_{rem,r}(t_{arr}) + \sum_{p'=1}^{p_j-1} \left( \left( \sum_{l'=1}^{L-1} g_{r,l',p'}(t_{arr}) \right) \cdot c \right)}{f_r}
                        \end{equation}
                        其中 $C_{rem,r}(t_{arr})$ 是 $t_{arr}$ 时刻RSU $r$ 上正在服务任务的剩余计算量 (若CPU空闲则为0)，$g_{r,l',p'}(t_{arr})$ 是 $t_{arr}$ 时刻RSU $r$ 队列 $(l',p')$ 中的数据量，$c$ 是处理密度，$f_r$ 是RSU $r$ 的计算能力。该公式估算在任务 $j$ 前面所有更高优先级任务及当前任务（若有）的处理时间。
                    \item 计算时延 ($T_{comp, j, r}$): $T_{comp, j, r} = C_j/f_r$。
                    \item 下载传输时延 ($T_{trans\_down, j, r}$): $T_{trans\_down, j, r} = S_j/R_{r,n}$。
                    \item 总时延预测 (缓存未命中): $T_{total, j, r}^{\text{pred, no-cache}} = T_{trans\_up, j, r} + T_{wait, j, r} + T_{comp, j, r} + T_{trans\_down, j, r}$。
                \end{itemize}
            \item 可行性检查: $T_{total, j, r}^{\text{pred, no-cache}} \leq T_{max,j} \times \Delta t$。
            \item 队列交互: 数据 $D_j$ 从车辆 $v_j$ 的 $q_{v_j,l,p}^t$ 移除。假设传输消耗一个时隙，任务将加入 RSU $r$ 的 $g_{r,l-1,p}$ 队列 (如果 $l>1$) 或一个特殊的到达队列。处理后从相应RSU队列移除。
        \end{itemize}
\end{itemize}

\subsection{模式三：RSU 间迁移 (评估 $k \to k'$ for $k, k' \in \mathcal{R}$)}
\textbf{前提}: RSU $k$ 存有待处理数据（该数据未在$k'$缓存），迁移到 RSU $k'$ 处理可能更优。
\begin{itemize}
    \item 动作: RSU $k$ 迁移某任务对应的数据块 (例如 $D_j$) 到 RSU $k'$。
    \item 迁移时延 ($T_{mig, k, k'}$): $T_{mig, k, k'}(D_j) = D_j / R_{k,k'}$ (见 Eq. \ref{eq:T_trans_mig_revised})。
    \item 目的: 优化任务整体端到端时延。评估需考虑迁移时延及后续在 $k'$ 的等待和计算时延（假设 $k'$ 未缓存结果）。
    \item 队列交互: 数据 $D_{mig,k,k',l,p}^t$ 从 $g_{k,l,p}^t$ 移除。假设迁移消耗一个时隙，数据将加入 $g_{k',l-1,p}$ 队列 (如果 $l>1$) 或一个特殊到达队列。
\end{itemize}

\subsection{模式四：卸载到 UAV (评估 $u \in \mathcal{U} \cap \mathcal{N}_j^{cand}$)}
\textbf{前提}: 候选 UAV $u \in \mathcal{N}_j^{cand}$，且车辆 $n=v_j$ 在 $u$ 的通信范围内。 (UAV $u$ 位置固定)
\begin{itemize}
    \item 动作: 车辆 $n=v_j$ 决定卸载任务 $j$ 到 UAV $u$。
    \item 时延分析 (预测):
        \begin{itemize}
            \item 上传传输时延: $T_{\text{trans\_up},j,u} = D_j / R_{n,u}$。
            \item 等待时延 (优先级队列): $T_{\text{wait},j,u}^{\text{pred}}$ 使用式 \eqref{eq:T_wait_u_priority} (长期平均) 或式(4) 的瞬时积压估计。
            \item 执行时延: $T_{\text{exec},j,u} = C_j / f_u$。
            \item 下载传输时延: $T_{\text{trans\_down},j,u} = S_j / R_{u,n}$。
            \item 总时延预测: $T_{\text{total},j,u}^{\text{pred}} = T_{\text{trans\_up},j,u} + T_{\text{wait},j,u}^{\text{pred}} + T_{\text{exec},j,u} + T_{\text{trans\_down},j,u}$。(参考 Eq. \ref{eq:T_total_u_revised})。
        \end{itemize}
    \item 可行性检查: $T_{\text{total},j,u}^{\text{pred}} \leq T_{max,j} \times \Delta t$
    \item 队列交互: 数据 $D_j$ 从车辆 $v_j$ 的 $q_{v_j,l,p}^t$ 移除。假设传输消耗一个时隙，任务将加入 UAV $u$ 的 $u_{u,l-1,p}$ 队列 (如果 $l>1$) 或一个特殊到达队列,处理后从相应UAV队列移除。
\end{itemize}

\newpage
\section{通信与计算模型 (公式汇总)}

\subsection{本地计算模型 (车辆 $n \in \mathcal{V}$)}
\begin{align}
    \text{处理能力 (数据量/时隙)}: \quad & D^{local}_{n} = \frac{f_{n} \Delta t}{c} \label{eq:D_local_n_revised_sec5} \\ 
    \text{计算时延 (任务 j, 秒)}: \quad & T_{comp, j, n} = \frac{C_j}{f_n} = \frac{c D_j}{f_n} \label{eq:T_comp_revised_sec5} \\ 
    \text{处理功率}: \quad & P^{comp}_{n} = \kappa_1 (f_{n})^3 \label{eq:P_comp_n_revised_sec5} \\ 
    \text{处理能耗 (每时隙, 若活动)}: \quad & E^{comp}_{n,t} = P^{comp}_{n} \cdot \tau_{active,n,t} \label{eq:E_comp_n_revised_sec5} 
\end{align}
其中 $\tau_{active,n,t}$ 是时隙 $t$ 内车辆 $n$ CPU的实际活动时长（$\le \Delta t$）。如果简化假设为整个时隙，则 $\tau_{active,n,t} = \Delta t$ (当有计算时)。

\subsection{无线通信模型 (链路 $a \to b$, e.g., V2I, V2U, U2V)}
假设节点 $a$ 向节点 $b$ 传输数据 $D$, 分配总带宽为 $B_{a,b}$ (由决策变量 $b_{a,b}^t$ 确定)。
%% MODIFIED: If node a or b is a UAV, its position pos_a or pos_b is fixed.
\begin{align}
    \text{距离}: \quad & d_{a,b}(t) = \lVert \text{pos}_a(t) - \text{pos}_b(t) \rVert \quad (\text{若 } a \text{ 或 } b \text{ 是UAV, 其位置为固定值}) \\
    \text{路径损耗 (dB, 示例模型)}: & \nonumber \\
    & L_{\text{LoS}}(d) = PL_0 + 10 \eta_{\text{LoS}} \log_{10}(d) + X_{\sigma, \text{LoS}} \\
    & L_{\text{NLoS}}(d) = PL_0 + 10 \eta_{\text{NLoS}} \log_{10}(d) + X_{\sigma, \text{NLoS}} \\
    \text{路径损耗 (dB, 综合)}: \quad & L_{a,b}(t) = P_{\text{LoS},a,b}(t) L_{\text{LoS}}(d_{a,b}(t)) + (1 - P_{\text{LoS},a,b}(t)) L_{\text{NLoS}}(d_{a,b}(t)) \\
    \text{信道增益 (线性)}: \quad & h_{a,b}(t) = 10^{-L_{a,b}(t)/10} \\
    \text{信噪干扰比}: \quad & \text{SINR}_{a,b}(t) = \frac{P_{tx,a} h_{a,b}(t)}{I_{ext,b}(t) + N_0 B_{a,b}^t} \label{eq:SINR_revised_sec5}\\ 
     & \text{其中 } I_{ext,b}(t) = \sum_{i \neq a, i \text{ interfering with } b} P_{tx,i} h_{i,b}(t) \text{ 是来自其他同频发射机的干扰} \nonumber \\
    \text{传输速率 (bps)}: \quad & R_{a,b}(t) = B_{a,b}^t \log_2 (1 + \text{SINR}_{a,b}(t)) \label{eq:Rate_revised_sec5} \\ 
    \text{传输时延 (数据 D, 秒)}: \quad & T_{trans, a, b}(D,t) = \frac{D}{R_{a,b}(t)} \label{eq:T_trans_revised_sec5} \\ 
    \text{传输量 (bits/时隙)}: \quad & D^{trans}_{a,b}(t) = R_{a,b}(t) \Delta t \label{eq:D_trans_revised_sec5} \\ 
    \text{传输能耗 (发送端 $a$, 每时隙)}: \quad & E^{tx}_{a,t} = P_{tx,a} \cdot \tau_{tx,a,t} \label{eq:E_trans_revised_sec5} 
\end{align}
其中 $PL_0$ 是参考路径损耗，$\eta$ 是路径损耗指数，$X_{\sigma}$ 是阴影衰落，$N_0$ 是噪声功率谱密度。$\tau_{tx,a,t}$ 是时隙 $t$ 内节点 $a$ 的实际发射时长。

\subsection{RSU 计算模型 (RSU $k \in \mathcal{R}$)}
\begin{align}
    \text{处理能力 (数据量/时隙)}: \quad & D^{proc}_{k} = \frac{f_{k} \Delta t}{c} \label{eq:D_proc_k_revised_sec5} \\ 
    \text{计算时延 (任务 j, 秒)}: \quad & T_{comp, j, k} = \frac{C_j}{f_k} = \frac{c D_j}{f_k} \label{eq:T_comp_k_revised_sec5} \\ 
    \text{处理功率}: \quad & P^{comp}_{k} = \kappa_2 (f_{k})^3 \label{eq:P_comp_k_revised_sec5} \\ 
    \text{处理能耗 (每时隙, 若激活)}: \quad & E^{comp}_{k,t} = \mu_k^t P^{comp}_{k} \cdot \tau_{active,k,t} \label{eq:E_comp_k_revised_sec5} 
\end{align}
其中 $\kappa_2$ 是 RSU 芯片相关的有效电容系数，$\tau_{active,k,t}$ 是 RSU $k$ 在时隙 $t$ CPU 的实际活动时长。

\subsection{RSU 间迁移通信模型 (链路 $k \to k'$, RSU $k$ 发送)}
采用与5.2节类似的无线通信模型，但可能使用不同的参数（如专用回程链路）。
\begin{align}
    \text{传输速率 (bps)}: \quad & R_{k,k'}(t) = B_{k,k'}^t \log_2 (1 + \text{SINR}_{k,k'}(t)) \label{eq:Rate_mig_revised_sec5} \\ 
    \text{迁移传输时延 (数据 D, 秒)}: \quad & T_{trans, k, k'}(D,t) = \frac{D}{R_{k,k'}(t)} \label{eq:T_trans_mig_revised} \\
    \text{传输量 (bits/时隙)}: \quad & D^{mig}_{k,k'}(t) = R_{k,k'}(t) \Delta t \label{eq:D_mig_revised_sec5} \\ 
    \text{传输能耗 (RSU $k$, 每时隙, 若迁移)}: \quad & E^{tx,mig}_{k,t} = \nu_{k,k'}^t P_{tx,k}^{mig} \cdot \tau_{tx,k,t}^{mig} \label{eq:E_mig_revised_sec5} 
\end{align}
$P_{tx,k}^{mig}$ 为 RSU 间迁移的发射功率，$\tau_{tx,k,t}^{mig}$ 为实际迁移发射时长。

\subsection{UAV 计算与排队模型 (UAV $u \in \mathcal{U}$, 多优先级 M/M/1 类型预测)}
针对任务 $j$ (大小 $D_j$, 复杂度 $C_j=c D_j$, 优先级 $p_j$) 进行预测。
\begin{align}
    \text{UAV 平均服务速率 (tasks/sec)}: \quad & \mu_u = f_u / C_{avg,u} \label{eq:mu_u_revised_sec5} \\ 
    \text{UAV 优先级 $i$ 任务平均到达率 (tasks/sec)}: \quad & \lambda_{i,u} = (\sum_{j': \text{assigned to } u, \text{Pri}(j')=i} \lambda_{j'}') / \Delta t \quad (\text{动态估计}) \label{eq:lambda_iu_revised_sec5} \\ 
    \text{UAV 优先级 $i$ 系统负载}: \quad & \rho_{i,u} = \lambda_{i,u} / \mu_u \quad (\text{需确保 } \sum_i \rho_{i,u} < 1) \label{eq:rho_iu_revised_sec5} \\ 
    \text{平均排队等待时延 (秒, 优先级 $p_j$)}: \quad & T_{\text{wait},j,u}^{\text{pred}} \text{ (见式\ref{eq:T_wait_u_priority})} \label{eq:T_wait_u_revised_sec5} \\ 
    \text{任务 $j$ 计算时延 (秒)}: \quad & T_{\text{exec},j,u} = C_j / f_u = c D_j / f_u \label{eq:T_exec_u_revised_sec5} \\ 
    \text{任务 $j$ 总时延预测 (秒)}: \quad & T_{\text{total},j,u}^{\text{pred}} = T_{trans, n, u}(D_j,t) + T_{\text{wait},j,u}^{\text{pred}} + T_{\text{exec},j,u} + T_{trans, u, n}(S_j,t) \label{eq:T_total_u_revised} \\
     & = (D_j / R_{n,u}(t)) + T_{\text{wait},j,u}^{\text{pred}} + (C_j / f_u) + (S_j / R_{u,n}(t)) \nonumber \\
    \text{任务 $j$ 计算能耗 (焦耳)}: \quad & E^{comp}_{u,j} = \kappa_3 f_u^2 C_j = \kappa_3 f_u^2 c D_j \label{eq:E_comp_u_revised_sec5} \\ 
    \text{时隙 $t$ 内UAV $u$ 计算总能耗}: \quad & E^{comp}_{u,t} = \kappa_3 (f_u(t))^3 \cdot \tau_{active,u,t} \quad \text{或基于处理任务的累积} \label{eq:E_comp_ut_revised_sec5} 
\end{align}
其中 $C_{avg,u}$ 是 UAV 平均任务复杂度，$\kappa_3$ 是 UAV 计算能耗系数。$f_u(t)$ 为时隙 $t$ UAV 的计算频率 (如果可调)。

\subsection{UAV 特定能耗模型} \label{sec:uav_energy_revised_sec5}

\subsubsection{UAV 通信能耗}
UAV 通信能耗包括接收任务数据和发送结果数据。
\begin{itemize}
    \item 接收能耗 (任务 $j$):
        \[ E^{rx}_{u,j}(t) = P_{rx,u} \times T_{trans, n, u}(D_j,t) = P_{rx,u} \frac{D_j}{R_{n,u}(t)} \]
    \item 发送能耗 (任务 $j$):\[ E^{tx}_{u,j}(t) = P_{tx,u} \times T_{trans, u, n}(S_j,t) = P_{tx,u} \frac{S_j}{R_{u,n}(t)} \]
    \item 任务 $j$ 的总通信能耗 (UAV 侧):\[ E^{comm}_{u,j}(t) = E^{rx}_{u,j}(t) + E^{tx}_{u,j}(t) \]
  \item 时隙 $t$ 内的通信总能耗 $E^{comm,t}_u$:
        \[ E^{comm,t}_u = P_{rx,u} \cdot \tau_{rx,u,t} + P_{tx,u} \cdot \tau_{tx,u,t} \]
        其中 $\tau_{rx,u,t}, \tau_{tx,u,t}$ 分别表示时隙 $t$ 内UAV $u$ 的实际接收/发送时长。
\end{itemize}

\subsubsection{UAV 飞行能耗}
%% MODIFIED: UAV is fixed, so flight energy is constant hovering energy.
由于 UAV 位置固定，其飞行能耗为保持悬停状态的能耗。
\begin{align}
    \text{悬停功率}: \quad & P_{hover,u} = P_0 + P_i \label{eq:P_hover_revised_sec5} \\
    \text{时隙 } t \text{ 内的悬停能耗}: \quad & E^{fly,t}_u = P_{hover,u} \Delta t \label{eq:E_fly_t_revised_sec5}
\end{align}
其中 $P_0$ 是悬停时的叶型功率系数，$P_i$ 是悬停时的诱导功率系数。原先与速度相关的飞行功率公式 \eqref{eq:P_fly_revised_sec5} 在此简化。

\newpage
\section{多优先级队列状态转移方程} \label{sec:state_transition_prio_revised}

假设任务数据从源节点队列 $(l_s, p)$ 取出后，经过一个时隙的传输/迁移，到达目标节点队列 $(l_d, p)$，其中 $l_d = l_s - 1$ (如果 $l_s > 1$)。如果 $l_s=1$，则任务必须在本时隙内处理，否则丢失。
对于新生成的任务 $D_{gen,n,l,p}^t$，它们直接进入车辆 $n$ 的队列 $(l,p)$。

\subsection{车辆队列更新 ($q_{n,l,p}^{t+1}$)}
车辆 $n$ 队列 $(l, p)$ 的数据量在时隙 $t+1$ 开始时更新为：
\begin{align}
    q_{n,l,p}^{t+1} = & \max\left\{0, q_{n,l+1,p}^t - D_{local,n,l+1,p}^t - \sum_{r \in \mathcal{R}} D_{off,n,r,l+1,p}^t - \sum_{u \in \mathcal{U}} D_{off,n,u,l+1,p}^t \right\} \cdot I(l < L) \nonumber \\
                      & + D_{gen,n,l,p}^t \quad \text{for } 1 \le l \le L, 1 \le p \le P \label{eq:q_update_prio_revised_sec6} 
\end{align}
其中 $I(l < L)$ 是指示函数。当 $l=L$ 时, 第一项中 $q_{n,L+1,p}^t = 0$ (无更长生命周期队列)。
$D_{local,n,l+1,p}^t$ 表示从车辆队列 $(l+1,p)$ 取出并本地处理的数据。
$D_{off,n,r,l+1,p}^t$ 表示从车辆队列 $(l+1,p)$ 取出并卸载到RSU $r$ (将进入RSU的 $(l,p)$ 队列，除非缓存命中)。
$D_{gen,n,l,p}^t$ 是直接在时隙 $t$ 生成并放入车辆队列 $(l,p)$ 的数据。

\subsection{RSU 队列更新 ($g_{k,l,p}^{t+1}$)}
RSU $k$ 队列 $(l, p)$ 的数据量在时隙 $t+1$ 开始时更新为 ($1 \le l \le L-1, 1 \le p \le P$):
\begin{align}
    g_{k,l,p}^{t+1} = & \quad \left( \text{来自RSU } k \text{ 自身上一生命周期队列 } (l+1,p) \text{ 的剩余数据} \right) \nonumber \\
                      & + \left( \text{从车辆的 } (l+1,p) \text{ 队列卸载过来且未在RSU } k \text{ 缓存命中的数据} \right) \nonumber \\
                      & + \left( \text{从其他RSU } k' \text{ 的 } (l+1,p) \text{ 队列迁移过来且未在RSU } k \text{ 缓存命中的数据} \right) \label{eq:g_update_prio_revised_conceptual_sec6} 
\end{align}
具体地：
\begin{align}
    \text{Term1}_{g_{k,l,p}} &= \begin{cases} \max\left\{0, g_{k,l+1,p}^t - D_{proc,k,l+1,p}^t - \sum_{k' \neq k} D_{mig,k,k',l+1,p}^t \right\}, & \text{if } l < L-1 \\ 0, & \text{if } l = L-1 \end{cases} \nonumber \\
    \text{Term2}_{g_{k,l,p}} &= \sum_{n \in \mathcal{V}} D_{off,n,k,l+1,p}^t \quad (\text{Data from vehicle queue } q_{n,l+1,p} \text{, assuming no cache hit at RSU } k) \nonumber \\
    \text{Term3}_{g_{k,l,p}} &= \begin{cases} \sum_{k' \in \mathcal{R}, k' \neq k} D_{mig,k',k,l+1,p}^t, & \text{if } l < L-1 \quad (\text{Data from RSU } k' \text{ queue } g_{k',l+1,p} \text{, assuming no cache hit at RSU } k) \\ 0, & \text{if } l = L-1 \end{cases} \nonumber \\
    g_{k,l,p}^{t+1} &= \text{Term1}_{g_{k,l,p}} + \text{Term2}_{g_{k,l,p}} + \text{Term3}_{g_{k,l,p}}
\end{align}
这里假设 $D_{off,n,k,l+1,p}^t$ 和 $D_{mig,k',k,l+1,p}^t$ 代表从源节点的 $(l+1,p)$ 队列取出，并在时隙 $t$ 结束时到达目标 RSU $k$ 并计入其 $(l,p)$ 队列的数据（特指那些未在RSU $k$ 缓存命中的任务数据）。

\subsection{UAV 队列更新 ($u_{u,l,p}^{t+1}$)}
UAV $u$ 队列 $(l, p)$ 的数据量在时隙 $t+1$ 开始时更新为 ($1 \le l \le L-1, 1 \le p \le P$):
\begin{align}
    \text{Term1}_{u_{u,l,p}} &= \begin{cases} \max\left\{0, u_{u,l+1,p}^t - D_{proc,u,l+1,p}^t \right\}, & \text{if } l < L-1 \\ 0, & \text{if } l = L-1 \end{cases} \nonumber \\
    \text{Term2}_{u_{u,l,p}} &= \sum_{n \in \mathcal{V}} D_{off,n,u,l+1,p}^t \nonumber \\
    u_{u,l,p}^{t+1} &= \text{Term1}_{u_{u,l,p}} + \text{Term2}_{u_{u,l,p}} \label{eq:u_update_prio_final_revised_sec6} 
\end{align}
这里假设 $D_{off,n,u,l+1,p}^t$ 代表从车辆的 $(l+1,p)$ 队列取出，并在时隙 $t$ 结束时到达 UAV $u$ 并计入其 $(l,p)$ 队列的数据。

\textit{注: RSU 和 UAV 的队列状态转移也受到其处理能力 $D_{proc,...}^t$ 和迁移决策 $D_{mig,...}^t$ 的影响。排队时延预测见式 \eqref{eq:T_wait_r_original} 和 \eqref{eq:T_wait_u_priority} (平均) 或式 \eqref{eq:T_wait_r_instantaneous_pred_sec4} (瞬时)。}

\subsection{数据丢失量 (时隙 $t$ 结束时发生)}
数据丢失发生在时隙 $t$ 结束时，指那些在时隙 $t$ 开始时位于生命周期为 1 的队列中 (任何优先级 $p$)，但在时隙 $t$ 内未能被处理或成功移出的数据。
\begin{itemize}
    \item 车辆丢失量 ($n$):
    \begin{align}
    D_{loss,n,p}^t &= \max\left\{0, q_{n,1,p}^t - D_{local,n,1,p}^t - \sum_{r \in \mathcal{R}} D_{off,n,r,1,p}^t - \sum_{u \in \mathcal{U}} D_{off,n,u,1,p}^t \right\} \\
    D_{loss,n}^t &= \sum_{p=1}^P D_{loss,n,p}^t
    \end{align}

    \item RSU 丢失量 ($k$):
    \begin{align}
    D_{loss,k,p}^t &= \max\left\{0, g_{k,1,p}^t - D_{proc,k,1,p}^t - \sum_{k' \in \mathcal{R}, k' \neq k} D_{mig,k,k',1,p}^t \right\} \\
    D_{loss,k}^t &= \sum_{p=1}^P D_{loss,k,p}^t
    \end{align}

    \item UAV 丢失量 ($u$):
    \begin{align}
    D_{loss,u,p}^t &= \max\left\{0, u_{u,1,p}^t - D_{proc,u,1,p}^t \right\} \\
    D_{loss,u}^t &= \sum_{p=1}^P D_{loss,u,p}^t
    \end{align}
\end{itemize}

\textbf{总丢失量 (时隙 $t$):}
\[
D_{loss}^t = \sum_{n \in \mathcal{V}} D_{loss,n}^t + \sum_{k \in \mathcal{R}} D_{loss,k}^t + \sum_{u \in \mathcal{U}} D_{loss,u}^t
\]
这些丢失的数据量对应于延迟要求无法满足的任务部分。


\newpage
\section{优化问题} \label{sec:optimization_revised}

\subsection{优化目标}
优化问题旨在最小化在一个有限或无限时间域内，任务的加权总延迟、系统总能耗和总数据丢失量的期望加权和。对于时隙 $t$ 的决策，我们关注瞬时或短期内的目标。
假设优化周期为 $T_{horizon}$ 个时隙。
\[
\min_{\{\mathbf{X}^t\}_{t=0}^{T_{horizon}-1}} \mathbb{E} \left[ \sum_{t=0}^{T_{horizon}-1} \left( \omega_T \sum_{j \in \mathcal{J}_t^{comp}} T_{total,j}(t) + \omega_E E_{total}^t + \omega_L D_{loss}^t \right) \right]
\]
其中:
\begin{itemize}
    %% MODIFIED: Removed {v_u^t} from X^t
    \item $\mathbf{X}^t$: 时隙 $t$ 的所有决策变量集合，包括 $\{x_{j,n}^t\}$, $\{D^t_{local,...}, D^t_{off,...}, D^t_{proc,...}, D^t_{mig,...} \}$,  $\{\mu^t_k, \nu^t_{k,k'} \}$，$\{b^t_{link} \}$ 以及缓存变量 $\{z^t_{j,r}\}$ 。
    \item $\mathcal{J}_t^{comp}$: 在时隙 $t$ 或由于时隙 $t$ 的决策而完成处理并返回总时延 $T_{total,j}(t)$ 的任务集合。
    \item $E_{total}^t$: 系统在时隙 $t$ 的总能耗 (定义见式 \eqref{eq:E_total_t_final_revised_sec7})。 
    \item $D_{loss}^t$: 系统在时隙 $t$ 的总数据丢失量。
    \item $\omega_T, \omega_E, \omega_L$: 对应的权重因子。
    \item $\mathbb{E}[\cdot]$: 期望操作，因为任务到达、信道变化等可能具有随机性。
\end{itemize}
决策变量包括：
\begin{itemize}
    \item \textbf{任务分配与数据流决策} $\mathbf{D}^t$: $\{D^t_{local,n,l,p}, D^t_{off,n,r,l,p}, D^t_{off,n,u,l,p}, D^t_{proc,k,l,p}, D^t_{mig,k,k',l,p} \}$ 等。
    \item \textbf{带宽分配决策} $\mathbf{b}^t$: $\{b^t_{link}\}$。
    %% REMOVED BY USER REQUEST: UAV control decision v_u^t removed
    % \item \textbf{UAV 控制决策} $\mathbf{v}^t$: UAV 速度 $\{v^t_u\}$ (可能还包括高度、方向等)。
    \item \textbf{缓存决策} $\mathbf{z}$: $\{z_{j,r}\}$ (通常为较长周期决策) 或 $\{z^t_{j,r}\}$ 。
\end{itemize}
$x_{j,n}^t$ 是一个关键的瞬时决策，任务的最终完成节点 $x_{j,n}$ 是这一系列时隙决策的最终结果。

\subsection{约束条件}
以下约束应在每个时隙 $t$ 满足：
\begin{align}
    \text{(C1)} \quad & \sum_{n \in \mathcal{N}} x_{j,n}^t \leq 1, && \forall j \in \mathcal{J}_t^{active} \\
    \text{(C2)} \quad & T_{total,j} \leq T_{max,j}\cdot \Delta t, && \forall j \in \mathcal{J} \\
    \text{(C3)} \quad & \sum_{i=1}^P \rho_{i,n} < 1, && \forall n \in \mathcal{R} \cup \mathcal{U} \\
    \text{(C4)} \quad & \sum_{m \in \mathcal{N}, m \neq n} b_{n,m}^t \leq B_{n,\text{up}}^{max}, && \forall n \in \mathcal{N} \quad &\text{(节点 $n$ 总发出带宽约束)} \\
                 \quad & \sum_{m \in \mathcal{N}, m \neq n} b_{m,n}^t \leq B_{n,\text{down}}^{max}, && \forall n \in \mathcal{N} \quad &\text{(节点 $n$ 总接收带宽约束)}\\
    \text{(C5)} \quad & \text{SINR}_{link}(t) \geq \gamma_{\min}, && \forall \text{active links} \\
    \text{(C6)} \quad & \sum_{j \in \mathcal{J}} z_{j,r} S_j \leq S_{cache,r}, && \forall r \in \mathcal{R} \\
    \text{(C7)} \quad & \sum_{t=0}^{T_{horizon}-1} E_{u,t}^{total} \leq E_{max,u}, && \forall u \in \mathcal{U} \\
    %% REMOVED BY USER REQUEST: UAV motion constraint (C8) removed as UAVs are fixed
    % \text{(C8)} \quad & \text{pos}_u(t+1) = f(\text{pos}_u(t), v_u^t, \Delta t), && \forall u \in \mathcal{U} \\
    \text{(C8)} \quad & \text{UAV 位置固定 (原C8移除)} \nonumber \\
    \text{(C9)} \quad & x_{j,n}^t \in \{0, 1\}, z_{j,r} \in \{0, 1\}, \mu_k^t \in \{0,1\}, \nu_{k,k'}^t \in \{0,1\} \\
    \text{(C10)} \quad& D_{...}^t \ge 0, b_{link}^t \ge 0
\end{align}

% 用itemize或段落补充每个约束的中文解释
\begin{itemize}
    \item (C1) 任务在t时刻最多分配给一个节点处理。$\mathcal{J}_t^{active}$ 指在时隙 $t$ 活跃且需要调度决策的任务。
    \item (C2) 最终端到端延迟约束。是对最终完成的任务的延迟要求,需要在决策过程中通过预测来保证。
    \item (C3) RSU/UAV 队列稳定性。$\rho_{i,n}$ 是节点 $n$ 上优先级 $i$ 的流量强度。
    \item (C4) 节点总发出/接收带宽约束。$B_n^{max}$ 是节点 $n$ 的总可用带宽。
    \item (C5) 通信质量。
    \item (C6) RSU缓存容量。
    \item (C7) UAV 总能耗预算。
    \item (C8) UAV 位置固定（原UAV运动约束已移除）。
    \item (C9) 二元决策变量。
    \item (C10) 非负决策变量。
\end{itemize}

%% MODIFIED: E_total^t's E_fly^t component uses the simplified hovering energy
系统在时隙 $t$ 的总能耗为：
\begin{align}
    E_{total}^t = & \sum_{n \in \mathcal{V}} (E^{comp}_{n,t} + E^{tx}_{n,t}) \quad (\text{车辆能耗: 计算 + 发射}) \nonumber \\
                  & + \sum_{k \in \mathcal{R}} (E^{comp}_{k,t} (\text{若计算}) + E^{tx,mig}_{k,t} + E^{rx,mig}_{k,t}) \quad (\text{RSU能耗: 计算 (若发生) + 迁移发射/接收}) \nonumber\\ 
                  & + \sum_{u \in \mathcal{U}} (E^{comp}_{u,t} + E^{comm,t}_u + E^{fly,t}_u) \quad (\text{UAV能耗: 计算 + 通信 + 悬停})
    \label{eq:E_total_t_final_revised_sec7} 
\end{align}
其中 $E^{rx,mig}_{k,t}$ 是RSU $k$ 在时隙 $t$ 接收迁移数据的能耗。$E^{comp}_{k,t}$ 仅在RSU $k$ 实际执行计算（即任务未缓存命中）时产生。UAV的 $E^{fly,t}_u$ 为其固定的悬停能耗。

%% REMOVED BY USER REQUEST: Section 8 (Vehicle Trajectory Prediction) is removed.
\iffalse 
\newpage
\section{车辆轨迹预测}
\label{sec:trajectory_prediction}

车辆轨迹预测对于边缘计算中的资源分配、任务卸载和UAV路径规划至关重要。

\subsection{模块目标}
\begin{itemize}
    \item 预测未来 $H_{pred}$ 个时隙内，每辆车 $v \in \mathcal{V}$ 的位置序列:
    \[ \text{PredPos}_v(t+1), \text{PredPos}_v(t+2), \dots, \text{PredPos}_v(t+H_{pred}) \]
\end{itemize}

\subsection{输入特征}
对于车辆 $v$ 在当前时隙 $t$，可用的输入特征包括：
\begin{itemize}
    \item \textbf{历史轨迹数据} (过去 $W_h$ 个时隙):
    \begin{itemize}
        \item 位置序列: $\{ \text{pos}_v(t-i) \}_{i=0}^{W_h-1}$
        \item 速度序列: $\{ \text{speed}_v(t-i) \}_{i=0}^{W_h-1}$
    \end{itemize}
     \item \textbf{道路网络信息} (若可用):
    \begin{itemize}
        \item 车辆 $v$ 当前所在路段的属性。
        \item 前方路网拓扑信息。
    \end{itemize}
    \item \textbf{其他上下文信息} (若可用): 交通状况、驾驶员意图（模拟中）。
\end{itemize}
 $\mathbf{F}_{v,t}^{traj}$ 表示用于轨迹预测的车辆 $v$ 在时隙 $t$ 的综合输入特征向量。

\subsection{预测模型}
\begin{itemize}
    \item \textbf{Seq2Seq},
    利用编码器-解码器架构处理时序依赖性。
    \begin{align*}
        \text{ContextVector}_v & = \text{Encoder}(\mathbf{F}_{v,t-W_h+1}^{traj}, \dots, \mathbf{F}_{v,t}^{traj}) \\
        \{\text{PredPos}_v(t+h)\}_{h=1}^{H_{pred}} & = \text{DecoderLSTM}(\text{ContextVector}_v)
    \end{align*}
\end{itemize}

\subsection{损失函数}
常用的损失函数是均方误差 (MSE)：
\begin{equation}
    \mathcal{L}_{traj} = \frac{1}{|\mathcal{V}_{pred}| H_{pred}} \sum_{v \in \mathcal{V}_{pred}} \sum_{h=1}^{H_{pred}} || \text{PredPos}_v(t+h) - \text{pos}_v(t+h) ||_2^2
\end{equation}
其中 $\mathcal{V}_{pred}$ 是被预测的车辆集合，$\text{pos}_v(t+h)$ 是车辆 $v$ 在未来时隙 $t+h$ 的真实位置。

\subsection{模块集成}
在每个DRL决策时隙 $t$ 开始时，调用此模块：
\[ \{\text{PredPos}_v(t+1), \dots, \text{PredPos}_v(t+H_{pred})\}_{v \in \mathcal{V}} = \text{TrajectoryPredictor}(\{\mathbf{F}_{v,t}^{traj}\}_{v \in \mathcal{V}}) \]
预测结果将作为DRL Agent状态的一部分。
\fi

\newpage
%% SECTION RENUMBERED: Old Section 9 is now Section 8
\section{MDP 模型} 
\label{sec:mdp_model}

\subsection{状态空间 ($\mathcal{S}$)}
状态 $S_t \in \mathcal{S}$ 在时隙 $t$ 包含以下信息：

\begin{itemize}
    \item \textbf{任务信息} $\mathcal{J}_t^{active}$ (当前活跃且待调度的任务集合):
    对于每个任务 $j \in \mathcal{J}_t^{active}$:
    \begin{itemize}
        \item 属性: $D_j, C_j, S_j, T_{rem,j}(t)$ (剩余最大容忍延迟), $p_j$ (优先级)。
        \item 生成任务的车辆: $v_j$.
        \item 当前所在队列:车辆 $v_j$ 的 $q_{n,l,p}$ 中的数据量或任务 $j$ 所属的数据块。
    \end{itemize}
    
    \item \textbf{节点信息}:
    \begin{itemize}
        \item \textbf{车辆 $v \in \mathcal{V}$}:
        \begin{itemize}
            \item 当前位置: $\text{pos}_v(t) = (x_v(t), y_v(t))$。
            %% REMOVED BY USER REQUEST: Predicted future trajectory removed from state
            % \item 预测未来轨迹: $\{\text{PredPos}_v(t+h)\}_{h=1}^{H_{pred}}$ 
            \item 本地队列状态: $\{q_{v,l,p}(t)\}_{\forall l,p}$ 。
        \end{itemize}
        \item \textbf{RSUs $r \in \mathcal{R}$}:
        \begin{itemize}
            \item 计算队列状态: $\{g_{n,l,p}(t)\}_{\forall l,p}$.
            \item 缓存状态 (针对相关任务 $j'$): $z_{j',k}(t) \in \{0,1\}$.
            \item 缓存占用: $\sum_{j' \in \mathcal{J}} z_{j',k}(t) S_{j'} / S_{cache,k}$.
            \item 计算能力: $f_k$.
        \end{itemize}
        \item \textbf{UAVs $u \in \mathcal{U}$}:
        \begin{itemize}
            %% MODIFIED: UAV position is fixed, velocity removed
            \item 当前位置: $\text{pos}_u$ (固定)。
            \item 剩余电量: $E_{rem,u}(t)$。
            \item 计算队列状态: $\{u_{u,l,p}(t)\}_{\forall l,p}$ 
            \item 计算负载: $\rho_u(t) = \sum_i \rho_{i,u}(t)$。
        \end{itemize}
    \end{itemize}

    \item \textbf{网络/信道信息} :
    对于关键通信链路 $link \in \{(v,r), (v,u), (u,v), (r,r')\}$:
    \begin{itemize}
        \item 估计数据传输速率: $R_{link}(t)$ (基于距离、SINR等)。
        \item 估计信道增益: $h_{link}(t)$。
    \end{itemize}

    \item \textbf{时间信息}:
    当前时隙索引 $t_{slot}$ 。
\end{itemize}
\textbf{状态表示}:
%% MODIFIED: PredPos_v removed from state encoding
$S_t = \text{Encode}(\mathcal{J}_t^{active}, \{\text{pos}_v(t), \dots\}_{v \in \mathcal{V}}, \{g_{r,l,p}(t), \dots\}_{r \in \mathcal{R}}, \{\text{pos}_u, E_{rem,u}(t), \dots\}_{u \in \mathcal{U}}, \dots)$

\subsection{动作空间 ($\mathcal{A}$)}
动作 $A_t \in \mathcal{A}$ 在时隙 $t$ 是一个组合动作，对应于您原文档中的决策变量:
\begin{itemize}
    \item \textbf{任务分配决策} (主要对应 $x_{j,n}^t$,  $D_{local,...}^t, D_{off,...}^t$):
    对于每个需要决策的任务 $j$ :
    $A_{task,j} \in \{ \text{ProcessLocally}(v_j) \} \cup \{ \text{Offload to RSU}(k) \}_{k \in \mathcal{R}} \cup \{ \text{Offload to UAV}(u) \}_{u \in \mathcal{U}}$.

    \item \textbf{RSU 缓存决策} (对应 $z_{j,k}^t$):
    当任务 $j$ 在RSU $k$ 处理完成后，或周期性地: $A_{cache,j,k} \in \{\text{Cache}, \text{NoCache}\}$.

    \item \textbf{RSU 间迁移决策} (对应 $\nu_{k,k'}^t$, $D_{mig,k,k',l,p}^t$):
    对于RSU $k$ 中的某些任务/数据块: $A_{migrate,j,k \to k'} \in \{\text{Migrate}, \text{NoMigrate}\}$.

    %% REMOVED BY USER REQUEST: UAV flight control decision removed
    % \item \textbf{UAV 飞行控制决策} (对应 $v_u^t$):
    % $A_{uav,u} = \mathbf{v}_u(t)$ (新的目标速度/方向或离散的移动指令)。

    \item \textbf{带宽分配决策} (对应 $b_{link}^t$):
    $A_{bw,link} = b_{link}^t$ (分配给链路 $link$ 的带宽)。
    
    \item \textbf{RSU激活决策} (对应 $\mu_k^t$):
    $A_{rsu\_active, k} = \mu_k^t \in \{0,1\}$.
\end{itemize}
\textbf{动作空间表示}: $A_t$ 包含上述所有具体决策的向量或结构。

\subsection{奖励函数 ($\mathcal{R}(S_t, A_t, S_{t+1})$)}
即时奖励 $R_t$ 在时隙 $t$ 旨在最小化加权总延迟、系统总能耗和总数据丢失量。
\begin{equation} \label{eq:reward_mdp_revised} %% Renumbered equation
    R_t = - \left( \omega_T \sum_{j \in \mathcal{J}_t^{comp}} T_{total,j}(t) + \omega_E E_{total}^t + \omega_L D_{loss}^t \right)
\end{equation}
其中:
\begin{itemize}
    \item $\mathcal{J}_t^{comp}$: 在时隙 $t$ 或由于时隙 $t$ 的决策而完成处理并返回结果的任务集合。
    \item $T_{total,j}(t)$: 任务 $j$ 的总端到端延迟 (从生成到结果返回)。
    %% MODIFIED: Reference to E_total^t which now uses simplified UAV hovering energy
    \item $E_{total}^t$: 系统在时隙 $t$ 的总能耗，定义见论文 Section 7, Eq.\eqref{eq:E_total_t_final_revised_sec7}。
        \begin{align*}
            E_{total}^t = & \sum_{n \in \mathcal{V}} (E^{comp}_{n,t} + E^{tx}_{n,t}) \\
                          & + \sum_{k \in \mathcal{R}} (E^{comp}_{k,t}  + E^{tx,mig}_{k,t} + E^{rx,mig}_{k,t}) \\
                          & + \sum_{u \in \mathcal{U}} (E^{comp}_{u,t} + E^{comm,t}_u + E^{fly,t}_u) (\text{其中 } E^{fly,t}_u \text{ 为悬停能耗})
        \end{align*}
    \item $D_{loss}^t$: 系统在时隙 $t$ 结束时，因超过最大容忍延迟而丢失的总数据量 (定义见论文 Section 6.4)。
    \item $\omega_T, \omega_E, \omega_L \ge 0$: 对应的权重因子，用于平衡不同优化目标。
\end{itemize}
\textbf{奖励塑形 (Reward Shaping)}: 
\begin{itemize}
    \item 对成功的缓存命中给予小的正奖励。
    \item 对队列长度的显著减少给予正奖励。
    \item 对UAV有效服务关键区域或车辆给予正奖励 (由于UAV位置固定，此项可能调整为衡量UAV服务效率)。
    \item 对违反软约束（如SINR略低于阈值、UAV电池接近临界值）给予负惩罚。
\end{itemize}

\subsection{状态转移概率 ($\mathcal{P}(S_{t+1} | S_t, A_t)$)}
%% MODIFIED: Removed reference to trajectory prediction module
状态转移由系统动态模型（队列更新方程、通信模型、能耗模型等以及环境的随机性（新任务生成服从 $\lambda_j'$，信道随机性，车辆轨迹的随机性）共同决定。

\subsection{折扣因子 ($\gamma$)}
$\gamma \in [0, 1]$， $\pi^*(A_t | S_t)$ 来最大化期望累积折扣奖励:
\[ J(\pi) = \mathbb{E}_{\pi} \left[ \sum_{k=0}^{\infty} \gamma^k R_{t+k+1} | S_t \right] \]

\subsection{DRL 算法选型}
\begin{itemize}
    \item \textbf{PPO (Proximal Policy Optimization)}: 稳定、易用和支持多种动作空间
    %% MODIFIED: SAC relevance might change if continuous UAV control is removed, but other continuous actions might exist
    \item \textbf{SAC (Soft Actor-Critic)}: 若仍有重要连续动作（如带宽分配），则适用。
    \item \textbf{MADDPG 算法}
\end{itemize}

\end{document}