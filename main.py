"""
主训练脚本
车联网边缘计算PPO训练
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import torch

from config.config import *
from env.mdp_environment import VehicularEdgeComputingEnv
from algorithms.ppo import PPOAgent, PPOTrainer
from utils.task_generator import TaskGenerator


def setup_directories():
    """设置保存目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"results/exp_{timestamp}"
    os.makedirs(save_dir, exist_ok=True)
    os.makedirs(f"{save_dir}/models", exist_ok=True)
    os.makedirs(f"{save_dir}/plots", exist_ok=True)
    return save_dir


def save_config(save_dir: str, configs: dict):
    """保存配置参数"""
    config_path = os.path.join(save_dir, "config.json")

    # 转换dataclass为字典
    config_dict = {}
    for name, config in configs.items():
        if hasattr(config, '__dict__'):
            config_dict[name] = config.__dict__
        else:
            config_dict[name] = config

    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)


def plot_training_results(save_dir: str, training_results: dict):
    """绘制训练结果 - 带置信区间"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 奖励曲线 - 添加滑动平均和置信区间
    episodes = range(len(training_results['episode_rewards']))
    rewards = training_results['episode_rewards']

    # 计算滑动平均和标准差
    window_size = min(50, len(rewards) // 10)  # 动态窗口大小
    if window_size > 1:
        moving_avg = []
        moving_std = []
        for i in range(len(rewards)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(rewards), i + window_size // 2 + 1)
            window_data = rewards[start_idx:end_idx]
            moving_avg.append(np.mean(window_data))
            moving_std.append(np.std(window_data))

        # 绘制原始数据（透明）
        axes[0, 0].plot(episodes, rewards, alpha=0.3, color='lightblue', label='Raw Data')

        # 绘制滑动平均
        axes[0, 0].plot(episodes, moving_avg, color='blue', linewidth=2, label='Moving Average')

        # 绘制置信区间
        upper_bound = np.array(moving_avg) + np.array(moving_std)
        lower_bound = np.array(moving_avg) - np.array(moving_std)
        axes[0, 0].fill_between(episodes, lower_bound, upper_bound, alpha=0.2, color='blue', label='±1σ Confidence')
    else:
        axes[0, 0].plot(episodes, rewards, color='blue')

    axes[0, 0].set_title('Episode Rewards with Confidence Interval')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].legend()
    axes[0, 0].grid(True)

    # episode长度 - 添加置信区间
    lengths = training_results['episode_lengths']
    if window_size > 1:
        moving_avg_len = []
        moving_std_len = []
        for i in range(len(lengths)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(lengths), i + window_size // 2 + 1)
            window_data = lengths[start_idx:end_idx]
            moving_avg_len.append(np.mean(window_data))
            moving_std_len.append(np.std(window_data))

        axes[0, 1].plot(episodes, lengths, alpha=0.3, color='lightgreen', label='Raw Data')
        axes[0, 1].plot(episodes, moving_avg_len, color='green', linewidth=2, label='Moving Average')

        upper_bound_len = np.array(moving_avg_len) + np.array(moving_std_len)
        lower_bound_len = np.array(moving_avg_len) - np.array(moving_std_len)
        axes[0, 1].fill_between(episodes, lower_bound_len, upper_bound_len, alpha=0.2, color='green', label='±1σ Confidence')
    else:
        axes[0, 1].plot(episodes, lengths, color='green')

    axes[0, 1].set_title('Episode Lengths with Confidence Interval')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Length')
    axes[0, 1].legend()
    axes[0, 1].grid(True)

    # 策略损失
    if training_results['training_metrics']:
        policy_losses = [m.get('policy_loss', 0) for m in training_results['training_metrics']]
        updates = range(len(policy_losses))
        axes[1, 0].plot(updates, policy_losses, color='red')
        axes[1, 0].set_title('Policy Loss')
        axes[1, 0].set_xlabel('Update')
        axes[1, 0].set_ylabel('Loss')
        axes[1, 0].grid(True)

        # 价值损失
        value_losses = [m.get('value_loss', 0) for m in training_results['training_metrics']]
        axes[1, 1].plot(updates, value_losses, color='orange')
        axes[1, 1].set_title('Value Loss')
        axes[1, 1].set_xlabel('Update')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'plots', 'training_curves.png'), dpi=300, bbox_inches='tight')
    plt.close()


def plot_performance_metrics(save_dir: str, metrics_history: list):
    """绘制性能指标 - 带置信区间"""
    if not metrics_history:
        return

    fig, axes = plt.subplots(2, 3, figsize=(18, 10))

    # 提取指标数据
    delay_data = [m.get('total_delay', 0) for m in metrics_history]
    energy_data = [m.get('energy_consumption', 0) for m in metrics_history]
    completion_data = [m.get('task_completion_rate', 0) for m in metrics_history]
    cache_hit_data = [m.get('cache_hit_rate', 0) for m in metrics_history]
    queue_util_data = [m.get('queue_utilization', 0) for m in metrics_history]

    timesteps = range(len(delay_data))

    # 计算滑动平均的窗口大小
    window_size = min(20, len(delay_data) // 10) if len(delay_data) > 10 else 1

    def plot_with_confidence(ax, data, title, ylabel, color):
        """绘制带置信区间的指标"""
        # 定义有效的浅色映射
        light_color_map = {
            'red': 'lightcoral',
            'orange': 'moccasin',
            'green': 'lightgreen',
            'blue': 'lightblue',
            'purple': 'plum',
            'navy': 'lightsteelblue'
        }

        if window_size > 1:
            moving_avg = []
            moving_std = []
            for i in range(len(data)):
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(data), i + window_size // 2 + 1)
                window_data = data[start_idx:end_idx]
                moving_avg.append(np.mean(window_data))
                moving_std.append(np.std(window_data))

            # 绘制原始数据（透明）
            light_color = light_color_map.get(color, 'lightgray')
            ax.plot(timesteps, data, alpha=0.3, color=light_color, label='Raw Data')

            # 绘制滑动平均
            ax.plot(timesteps, moving_avg, color=color, linewidth=2, label='Moving Average')

            # 绘制置信区间
            upper_bound = np.array(moving_avg) + np.array(moving_std)
            lower_bound = np.array(moving_avg) - np.array(moving_std)
            ax.fill_between(timesteps, lower_bound, upper_bound, alpha=0.2, color=color, label='±1σ Confidence')
            ax.legend(fontsize=8)
        else:
            ax.plot(timesteps, data, color=color)

        ax.set_title(title)
        ax.set_ylabel(ylabel)
        ax.grid(True)

    # 绘制各个指标
    plot_with_confidence(axes[0, 0], delay_data, 'Total Delay with CI', 'Delay (time slots)', 'red')
    plot_with_confidence(axes[0, 1], energy_data, 'Energy Consumption with CI', 'Energy', 'orange')
    plot_with_confidence(axes[0, 2], completion_data, 'Task Completion Rate with CI', 'Completion Rate', 'green')
    plot_with_confidence(axes[1, 0], cache_hit_data, 'Cache Hit Rate with CI', 'Hit Rate', 'blue')
    plot_with_confidence(axes[1, 1], queue_util_data, 'Queue Utilization with CI', 'Utilization', 'purple')

    # 综合性能指标
    combined_score = []
    for i in range(len(delay_data)):
        score = (completion_data[i] * 0.4 +
                cache_hit_data[i] * 0.2 +
                (1 - min(delay_data[i] / 20.0, 1.0)) * 0.3 +
                queue_util_data[i] * 0.1)
        combined_score.append(score)

    plot_with_confidence(axes[1, 2], combined_score, 'Combined Performance Score with CI', 'Score', 'navy')

    for ax in axes.flat:
        ax.set_xlabel('Timestep')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'plots', 'performance_metrics.png'), dpi=300, bbox_inches='tight')
    plt.close()


def evaluate_agent(env, agent, num_episodes: int = 10):
    """评估智能体性能"""
    eval_results = {
        'episode_rewards': [],
        'episode_lengths': [],
        'performance_metrics': [],
        'task_completion_rates': [],
        'average_delays': []
    }

    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False

        while not done:
            action, _, _ = agent.get_action(state, deterministic=True)
            state, reward, done, info = env.step(action.squeeze())

            episode_reward += reward
            episode_length += 1

            if 'metrics' in info:
                eval_results['performance_metrics'].append(info['metrics'])

        eval_results['episode_rewards'].append(episode_reward)
        eval_results['episode_lengths'].append(episode_length)

        # 提取任务完成率和平均延迟
        if eval_results['performance_metrics']:
            last_metrics = eval_results['performance_metrics'][-1]
            eval_results['task_completion_rates'].append(
                last_metrics.get('task_completion_rate', 0)
            )
            eval_results['average_delays'].append(
                last_metrics.get('total_delay', 0)
            )

    # 计算统计数据
    stats = {
        'mean_reward': np.mean(eval_results['episode_rewards']),
        'std_reward': np.std(eval_results['episode_rewards']),
        'mean_length': np.mean(eval_results['episode_lengths']),
        'mean_completion_rate': np.mean(eval_results['task_completion_rates']),
        'mean_delay': np.mean(eval_results['average_delays'])
    }

    return eval_results, stats


def main():
    """主函数"""
    print("=== 车联网边缘计算PPO训练 ===")

    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)

    # 创建配置
    system_config = SystemConfig()
    task_config = TaskConfig()
    ppo_config = PPOConfig()

    # 设置保存目录
    save_dir = setup_directories()
    print(f"结果保存路径: {save_dir}")

    # 保存配置
    save_config(save_dir, {
        'system': system_config,
        'task': task_config,
        'ppo': ppo_config
    })

    # 创建环境
    print("创建仿真环境...")
    env = VehicularEdgeComputingEnv(system_config)

    # 获取状态和动作空间信息
    state_dim = env.observation_space.shape[0]
    action_space = env.action_space

    print(f"状态空间维度: {state_dim}")
    print(f"动作空间: {action_space}")

    # 创建PPO智能体
    print("创建PPO智能体...")
    agent = PPOAgent(state_dim, action_space, ppo_config)

    # 创建训练器
    trainer = PPOTrainer(env, agent, ppo_config)    # 开始训练
    print("开始训练...")
    print(f"训练配置: {ppo_config.n_epochs} epochs, {ppo_config.total_timesteps} total timesteps")

    training_results = trainer.train(ppo_config.total_timesteps)

    print("训练完成!")

    # 保存模型
    model_path = os.path.join(save_dir, "models", "final_model.pt")
    agent.save(model_path)
    print(f"模型已保存: {model_path}")
      # 评估智能体
    print("评估智能体性能...")
    eval_results, eval_stats = evaluate_agent(env, agent, num_episodes=10)  # 减少评估episode数

    print("=== 评估结果 ===")
    print(f"平均奖励: {eval_stats['mean_reward']:.2f} ± {eval_stats['std_reward']:.2f}")
    print(f"平均episode长度: {eval_stats['mean_length']:.2f}")
    print(f"平均任务完成率: {eval_stats['mean_completion_rate']:.3f}")
    print(f"平均延迟: {eval_stats['mean_delay']:.2f}")

    # 保存评估结果
    eval_path = os.path.join(save_dir, "evaluation_results.json")
    with open(eval_path, 'w') as f:
        json.dump({
            'eval_results': eval_results,
            'eval_stats': eval_stats
        }, f, indent=2)

    # 绘制结果
    print("生成可视化图表...")
    plot_training_results(save_dir, training_results)

    if eval_results['performance_metrics']:
        plot_performance_metrics(save_dir, eval_results['performance_metrics'])

    print(f"训练完成! 所有结果保存在: {save_dir}")


if __name__ == "__main__":
    main()
