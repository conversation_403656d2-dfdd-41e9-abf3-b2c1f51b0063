"""
主训练脚本
车联网边缘计算PPO训练
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import torch

from config.config import *
from env.mdp_environment import VehicularEdgeComputingEnv
from algorithms.ppo import PPOAgent, PPOTrainer
from utils.task_generator import TaskGenerator


def setup_directories():
    """设置保存目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"results/exp_{timestamp}"
    os.makedirs(save_dir, exist_ok=True)
    os.makedirs(f"{save_dir}/models", exist_ok=True)
    os.makedirs(f"{save_dir}/plots", exist_ok=True)
    return save_dir


def save_config(save_dir: str, configs: dict):
    """保存配置参数"""
    config_path = os.path.join(save_dir, "config.json")

    # 转换dataclass为字典
    config_dict = {}
    for name, config in configs.items():
        if hasattr(config, '__dict__'):
            config_dict[name] = config.__dict__
        else:
            config_dict[name] = config

    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)


def plot_training_results(save_dir: str, training_results: dict):
    """绘制训练结果"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 奖励曲线
    episodes = range(len(training_results['episode_rewards']))
    axes[0, 0].plot(episodes, training_results['episode_rewards'])
    axes[0, 0].set_title('Episode Rewards')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].grid(True)

    # episode长度
    axes[0, 1].plot(episodes, training_results['episode_lengths'])
    axes[0, 1].set_title('Episode Lengths')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Length')
    axes[0, 1].grid(True)

    # 策略损失
    if training_results['training_metrics']:
        policy_losses = [m.get('policy_loss', 0) for m in training_results['training_metrics']]
        updates = range(len(policy_losses))
        axes[1, 0].plot(updates, policy_losses)
        axes[1, 0].set_title('Policy Loss')
        axes[1, 0].set_xlabel('Update')
        axes[1, 0].set_ylabel('Loss')
        axes[1, 0].grid(True)

        # 价值损失
        value_losses = [m.get('value_loss', 0) for m in training_results['training_metrics']]
        axes[1, 1].plot(updates, value_losses)
        axes[1, 1].set_title('Value Loss')
        axes[1, 1].set_xlabel('Update')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'plots', 'training_curves.png'))
    plt.close()


def plot_performance_metrics(save_dir: str, metrics_history: list):
    """绘制性能指标"""
    if not metrics_history:
        return

    fig, axes = plt.subplots(2, 3, figsize=(18, 10))

    # 提取指标数据
    delay_data = [m.get('total_delay', 0) for m in metrics_history]
    energy_data = [m.get('energy_consumption', 0) for m in metrics_history]
    completion_data = [m.get('task_completion_rate', 0) for m in metrics_history]
    cache_hit_data = [m.get('cache_hit_rate', 0) for m in metrics_history]
    queue_util_data = [m.get('queue_utilization', 0) for m in metrics_history]

    timesteps = range(len(delay_data))

    # 绘制各个指标
    axes[0, 0].plot(timesteps, delay_data)
    axes[0, 0].set_title('Total Delay')
    axes[0, 0].set_ylabel('Delay (time slots)')
    axes[0, 0].grid(True)

    axes[0, 1].plot(timesteps, energy_data)
    axes[0, 1].set_title('Energy Consumption')
    axes[0, 1].set_ylabel('Energy')
    axes[0, 1].grid(True)

    axes[0, 2].plot(timesteps, completion_data)
    axes[0, 2].set_title('Task Completion Rate')
    axes[0, 2].set_ylabel('Completion Rate')
    axes[0, 2].grid(True)

    axes[1, 0].plot(timesteps, cache_hit_data)
    axes[1, 0].set_title('Cache Hit Rate')
    axes[1, 0].set_ylabel('Hit Rate')
    axes[1, 0].grid(True)

    axes[1, 1].plot(timesteps, queue_util_data)
    axes[1, 1].set_title('Queue Utilization')
    axes[1, 1].set_ylabel('Utilization')
    axes[1, 1].grid(True)

    # 综合性能指标
    combined_score = []
    for i in range(len(delay_data)):
        score = (completion_data[i] * 0.4 +
                cache_hit_data[i] * 0.2 +
                (1 - min(delay_data[i] / 20.0, 1.0)) * 0.3 +
                queue_util_data[i] * 0.1)
        combined_score.append(score)

    axes[1, 2].plot(timesteps, combined_score)
    axes[1, 2].set_title('Combined Performance Score')
    axes[1, 2].set_ylabel('Score')
    axes[1, 2].grid(True)

    for ax in axes.flat:
        ax.set_xlabel('Timestep')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'plots', 'performance_metrics.png'))
    plt.close()


def evaluate_agent(env, agent, num_episodes: int = 10):
    """评估智能体性能"""
    eval_results = {
        'episode_rewards': [],
        'episode_lengths': [],
        'performance_metrics': [],
        'task_completion_rates': [],
        'average_delays': []
    }

    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False

        while not done:
            action, _, _ = agent.get_action(state, deterministic=True)
            state, reward, done, info = env.step(action.squeeze())

            episode_reward += reward
            episode_length += 1

            if 'metrics' in info:
                eval_results['performance_metrics'].append(info['metrics'])

        eval_results['episode_rewards'].append(episode_reward)
        eval_results['episode_lengths'].append(episode_length)

        # 提取任务完成率和平均延迟
        if eval_results['performance_metrics']:
            last_metrics = eval_results['performance_metrics'][-1]
            eval_results['task_completion_rates'].append(
                last_metrics.get('task_completion_rate', 0)
            )
            eval_results['average_delays'].append(
                last_metrics.get('total_delay', 0)
            )

    # 计算统计数据
    stats = {
        'mean_reward': np.mean(eval_results['episode_rewards']),
        'std_reward': np.std(eval_results['episode_rewards']),
        'mean_length': np.mean(eval_results['episode_lengths']),
        'mean_completion_rate': np.mean(eval_results['task_completion_rates']),
        'mean_delay': np.mean(eval_results['average_delays'])
    }

    return eval_results, stats


def main():
    """主函数"""
    print("=== 车联网边缘计算PPO训练 ===")

    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)

    # 创建配置
    system_config = SystemConfig()
    task_config = TaskConfig()
    ppo_config = PPOConfig()

    # 设置保存目录
    save_dir = setup_directories()
    print(f"结果保存路径: {save_dir}")

    # 保存配置
    save_config(save_dir, {
        'system': system_config,
        'task': task_config,
        'ppo': ppo_config
    })

    # 创建环境
    print("创建仿真环境...")
    env = VehicularEdgeComputingEnv(system_config)

    # 获取状态和动作空间信息
    state_dim = env.observation_space.shape[0]
    action_space = env.action_space

    print(f"状态空间维度: {state_dim}")
    print(f"动作空间: {action_space}")

    # 创建PPO智能体
    print("创建PPO智能体...")
    agent = PPOAgent(state_dim, action_space, ppo_config)

    # 创建训练器
    trainer = PPOTrainer(env, agent, ppo_config)    # 开始训练
    print("开始训练...")
    total_timesteps = 5000  # 大幅减少训练步数，快速测试

    training_results = trainer.train(total_timesteps)

    print("训练完成!")

    # 保存模型
    model_path = os.path.join(save_dir, "models", "final_model.pt")
    agent.save(model_path)
    print(f"模型已保存: {model_path}")
      # 评估智能体
    print("评估智能体性能...")
    eval_results, eval_stats = evaluate_agent(env, agent, num_episodes=10)  # 减少评估episode数

    print("=== 评估结果 ===")
    print(f"平均奖励: {eval_stats['mean_reward']:.2f} ± {eval_stats['std_reward']:.2f}")
    print(f"平均episode长度: {eval_stats['mean_length']:.2f}")
    print(f"平均任务完成率: {eval_stats['mean_completion_rate']:.3f}")
    print(f"平均延迟: {eval_stats['mean_delay']:.2f}")

    # 保存评估结果
    eval_path = os.path.join(save_dir, "evaluation_results.json")
    with open(eval_path, 'w') as f:
        json.dump({
            'eval_results': eval_results,
            'eval_stats': eval_stats
        }, f, indent=2)

    # 绘制结果
    print("生成可视化图表...")
    plot_training_results(save_dir, training_results)

    if eval_results['performance_metrics']:
        plot_performance_metrics(save_dir, eval_results['performance_metrics'])

    print(f"训练完成! 所有结果保存在: {save_dir}")


if __name__ == "__main__":
    main()
