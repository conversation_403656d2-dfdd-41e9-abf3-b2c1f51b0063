"""
调试奖励和任务生成问题
"""

import numpy as np
from config.config import *
from env.mdp_environment import VehicularEdgeComputingEnv

def debug_environment():
    """调试环境奖励问题"""
    print("=== 调试奖励和任务生成 ===")
    
    # 创建配置
    system_config = SystemConfig()
    
    # 创建环境
    env = VehicularEdgeComputingEnv(system_config)
    
    print(f"任务到达率: {env.task_config.task_arrival_rate}")
    print(f"车辆数量: {len(env.vehicles)}")
    print(f"RSU数量: {len(env.rsus)}")
    print(f"UAV数量: {len(env.uavs)}")
    
    # 重置环境
    state = env.reset()
    print(f"初始状态形状: {state.shape}")
    
    # 运行几步，检查奖励和任务生成
    total_reward = 0
    for step in range(10):
        print(f"\n--- 步骤 {step+1} ---")
        
        # 随机动作
        action = env.action_space.sample()
        print(f"动作: {action}")
        
        # 执行步骤
        next_state, reward, done, info = env.step(action)
        
        print(f"奖励: {reward:.6f}")
        print(f"活跃任务数: {info.get('num_active_tasks', 0)}")
        print(f"当前时间: {info.get('current_time', 0)}")
        
        # 检查任务状态
        completed_tasks = [t for t in env.tasks if t.status.name == 'COMPLETED']
        failed_tasks = [t for t in env.tasks if t.status.name == 'FAILED']
        waiting_tasks = [t for t in env.tasks if t.status.name == 'WAITING']
        processing_tasks = [t for t in env.tasks if t.status.name == 'PROCESSING']
        
        print(f"任务统计:")
        print(f"  - 总任务数: {len(env.tasks)}")
        print(f"  - 等待中: {len(waiting_tasks)}")
        print(f"  - 处理中: {len(processing_tasks)}")
        print(f"  - 已完成: {len(completed_tasks)}")
        print(f"  - 已失败: {len(failed_tasks)}")
        
        # 检查能量消耗
        vehicle_energy = sum(v.energy_level for v in env.vehicles)
        uav_energy = sum(u.energy_level for u in env.uavs)
        print(f"能量状态:")
        print(f"  - 车辆总能量: {vehicle_energy:.2f}")
        print(f"  - UAV总能量: {uav_energy:.2f}")
        
        total_reward += reward
        
        if done:
            print("Episode结束")
            break
    
    print(f"\n总奖励: {total_reward:.6f}")
    print(f"平均奖励: {total_reward/10:.6f}")
    
    # 检查奖励计算组件
    print("\n=== 奖励计算分析 ===")
    
    # 手动计算各部分奖励
    delay_penalty = 0.0
    for task in env.tasks:
        if hasattr(task.status, 'name'):
            if task.status.name == 'COMPLETED' and hasattr(task, 'completion_time'):
                delay = task.completion_time - task.arrival_time
                delay_penalty -= delay * env.opt_config.delay_weight
                print(f"任务 {task.task_id}: 延迟={delay}, 延迟惩罚={delay * env.opt_config.delay_weight}")
            elif task.status.name == 'FAILED':
                delay_penalty -= env.opt_config.failure_penalty
                print(f"任务 {task.task_id}: 失败惩罚={env.opt_config.failure_penalty}")
    
    # 能耗惩罚
    total_energy = sum(v.energy_level for v in env.vehicles) + sum(u.energy_level for u in env.uavs)
    max_total_energy = (len(env.vehicles) * env.vehicle_config.max_energy + 
                       len(env.uavs) * env.uav_config.max_energy)
    energy_consumed = max_total_energy - total_energy
    energy_penalty = -env.opt_config.energy_weight * energy_consumed
    
    # 完成奖励
    completed_tasks = [t for t in env.tasks if hasattr(t.status, 'name') and t.status.name == 'COMPLETED']
    completion_reward = len(completed_tasks) * env.opt_config.completion_reward
    
    print(f"延迟惩罚: {delay_penalty:.6f}")
    print(f"能耗惩罚: {energy_penalty:.6f} (消耗能量: {energy_consumed:.2f})")
    print(f"完成奖励: {completion_reward:.6f} (完成任务: {len(completed_tasks)})")
    print(f"计算总奖励: {delay_penalty + energy_penalty + completion_reward:.6f}")

if __name__ == "__main__":
    debug_environment()
