{"eval_results": {"episode_rewards": [-5417.3451324416255, -5539.751654683903, -6537.915061605451, -3793.33246680346, -6829.96984564737, -5124.299376391803, -4640.834068250415, -6951.841424559861, -5581.672925060231, -4017.598156275426], "episode_lengths": [50, 50, 50, 50, 50, 50, 50, 50, 50, 50], "performance_metrics": [{"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 0.8, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9973333333333333}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.8888888888888888, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9937037037037038}, {"total_delay": 1.8888888888888888, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9937037037037038}, {"total_delay": 2.090909090909091, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.993030303030303}, {"total_delay": 2.4166666666666665, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9919444444444444}, {"total_delay": 2.230769230769231, "energy_consumption": 0.0, "task_completion_rate": 0.8666666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9481196581196581}, {"total_delay": 2.642857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9495238095238095}, {"total_delay": 2.466666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.8823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.952562091503268}, {"total_delay": 2.466666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.8823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.952562091503268}, {"total_delay": 2.375, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9394517543859648}, {"total_delay": 2.2941176470588234, "energy_consumption": 0.0, "task_completion_rate": 0.8095238095238095, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9288608776844072}, {"total_delay": 2.388888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.8181818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9314309764309764}, {"total_delay": 2.95, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9346111111111112}, {"total_delay": 3.4761904761904763, "energy_consumption": 0.0, "task_completion_rate": 0.7777777777777778, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9143386243386243}, {"total_delay": 3.4761904761904763, "energy_consumption": 0.0, "task_completion_rate": 0.7777777777777778, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9143386243386243}, {"total_delay": 3.260869565217391, "energy_consumption": 0.0, "task_completion_rate": 0.71875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8953804347826088}, {"total_delay": 3.1666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.7272727272727273, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8985353535353536}, {"total_delay": 3.12, "energy_consumption": 0.0, "task_completion_rate": 0.6944444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8877481481481482}, {"total_delay": 3.076923076923077, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8786324786324786}, {"total_delay": 3.074074074074074, "energy_consumption": 0.0, "task_completion_rate": 0.6585365853658537, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.875931948208371}, {"total_delay": 3.074074074074074, "energy_consumption": 0.0, "task_completion_rate": 0.5869565217391305, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8520719269994633}, {"total_delay": 3.1379310344827585, "energy_consumption": 0.0, "task_completion_rate": 0.58, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8495402298850575}, {"total_delay": 3.1379310344827585, "energy_consumption": 0.0, "task_completion_rate": 0.58, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8495402298850575}, {"total_delay": 3.193548387096774, "energy_consumption": 0.0, "task_completion_rate": 0.5535714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8405453149001536}, {"total_delay": 3.125, "energy_consumption": 0.0, "task_completion_rate": 0.5333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8340277777777777}, {"total_delay": 3.125, "energy_consumption": 0.0, "task_completion_rate": 0.5245901639344263, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8311133879781422}, {"total_delay": 2.9705882352941178, "energy_consumption": 0.0, "task_completion_rate": 0.53125, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8338480392156863}, {"total_delay": 3.5428571428571427, "energy_consumption": 0.0, "task_completion_rate": 0.5384615384615384, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8343443223443222}, {"total_delay": 4.055555555555555, "energy_consumption": 0.0, "task_completion_rate": 0.5373134328358209, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8322526257600885}, {"total_delay": 3.945945945945946, "energy_consumption": 0.0, "task_completion_rate": 0.5138888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8248098098098099}, {"total_delay": 3.945945945945946, "energy_consumption": 0.0, "task_completion_rate": 0.4868421052631579, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8157942152678995}, {"total_delay": 3.8684210526315788, "energy_consumption": 0.0, "task_completion_rate": 0.48717948717948717, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8161650922177238}, {"total_delay": 4.282051282051282, "energy_consumption": 0.0, "task_completion_rate": 0.4936708860759494, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8169501244184788}, {"total_delay": 4.282051282051282, "energy_consumption": 0.0, "task_completion_rate": 0.4875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8148931623931625}, {"total_delay": 4.6, "energy_consumption": 0.0, "task_completion_rate": 0.47619047619047616, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.810063492063492}, {"total_delay": 4.5, "energy_consumption": 0.0, "task_completion_rate": 0.4827586206896552, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8125862068965516}, {"total_delay": 4.363636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.4943820224719101, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8169152196118489}, {"total_delay": 4.288888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.4787234042553192, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8119448384554767}, {"total_delay": 4.239130434782608, "energy_consumption": 0.0, "task_completion_rate": 0.46464646464646464, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8074183867662129}, {"total_delay": 4.191489361702128, "energy_consumption": 0.0, "task_completion_rate": 0.47, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8093617021276596}, {"total_delay": 4.145833333333333, "energy_consumption": 0.0, "task_completion_rate": 0.46153846153846156, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8066933760683761}, {"total_delay": 4.489795918367347, "energy_consumption": 0.0, "task_completion_rate": 0.45794392523364486, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8043486553499904}, {"total_delay": 4.82, "energy_consumption": 0.0, "task_completion_rate": 0.45045045045045046, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8007501501501503}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.6666666666666666, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9977777777777778}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 1.1666666666666667, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961111111111111}, {"total_delay": 1.1666666666666667, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961111111111111}, {"total_delay": 1.1428571428571428, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961904761904762}, {"total_delay": 1.7777777777777777, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.994074074074074}, {"total_delay": 1.6, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9946666666666667}, {"total_delay": 1.5454545454545454, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9948484848484848}, {"total_delay": 1.4166666666666667, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9952777777777778}, {"total_delay": 1.3076923076923077, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9956410256410256}, {"total_delay": 1.8571428571428572, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9938095238095238}, {"total_delay": 1.7333333333333334, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9942222222222222}, {"total_delay": 1.625, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9945833333333334}, {"total_delay": 1.5294117647058822, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9763834422657952}, {"total_delay": 2.111111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9596296296296297}, {"total_delay": 2.6842105263157894, "energy_consumption": 0.0, "task_completion_rate": 0.8636363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9455980861244019}, {"total_delay": 2.6842105263157894, "energy_consumption": 0.0, "task_completion_rate": 0.76, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9110526315789474}, {"total_delay": 3.1904761904761907, "energy_consumption": 0.0, "task_completion_rate": 0.7777777777777778, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9152910052910053}, {"total_delay": 3.727272727272727, "energy_consumption": 0.0, "task_completion_rate": 0.6875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8834090909090909}, {"total_delay": 3.5652173913043477, "energy_consumption": 0.0, "task_completion_rate": 0.696969696969697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8871058410188845}, {"total_delay": 3.5652173913043477, "energy_consumption": 0.0, "task_completion_rate": 0.696969696969697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8871058410188845}, {"total_delay": 3.5, "energy_consumption": 0.0, "task_completion_rate": 0.6857142857142857, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8835714285714286}, {"total_delay": 3.4, "energy_consumption": 0.0, "task_completion_rate": 0.6410256410256411, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.869008547008547}, {"total_delay": 3.3461538461538463, "energy_consumption": 0.0, "task_completion_rate": 0.6190476190476191, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8618620268620268}, {"total_delay": 3.1785714285714284, "energy_consumption": 0.0, "task_completion_rate": 0.6222222222222222, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.863478835978836}, {"total_delay": 3.0689655172413794, "energy_consumption": 0.0, "task_completion_rate": 0.6041666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8578256704980842}, {"total_delay": 3.0, "energy_consumption": 0.0, "task_completion_rate": 0.5769230769230769, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8489743589743589}, {"total_delay": 3.0, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8418518518518519}, {"total_delay": 3.0, "energy_consumption": 0.0, "task_completion_rate": 0.543859649122807, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.837953216374269}, {"total_delay": 3.0, "energy_consumption": 0.0, "task_completion_rate": 0.5166666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8288888888888889}, {"total_delay": 3.0606060606060606, "energy_consumption": 0.0, "task_completion_rate": 0.5238095238095238, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8310678210678212}, {"total_delay": 3.0588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.5074626865671642, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8256248170910155}, {"total_delay": 3.138888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.4931506849315068, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8205872653475392}, {"total_delay": 3.6486486486486487, "energy_consumption": 0.0, "task_completion_rate": 0.4805194805194805, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8146776646776647}, {"total_delay": 3.5526315789473686, "energy_consumption": 0.0, "task_completion_rate": 0.4810126582278481, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8151621141461248}, {"total_delay": 3.5526315789473686, "energy_consumption": 0.0, "task_completion_rate": 0.475, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8131578947368422}, {"total_delay": 3.4, "energy_consumption": 0.0, "task_completion_rate": 0.4819277108433735, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8159759036144578}, {"total_delay": 3.341463414634146, "energy_consumption": 0.0, "task_completion_rate": 0.4823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8163127690100431}, {"total_delay": 3.341463414634146, "energy_consumption": 0.0, "task_completion_rate": 0.4659090909090909, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8108314855875832}, {"total_delay": 3.2857142857142856, "energy_consumption": 0.0, "task_completion_rate": 0.45652173913043476, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8078881987577639}, {"total_delay": 3.1818181818181817, "energy_consumption": 0.0, "task_completion_rate": 0.4444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8042087542087542}, {"total_delay": 3.577777777777778, "energy_consumption": 0.0, "task_completion_rate": 0.4368932038834951, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8003718087019057}, {"total_delay": 3.9782608695652173, "energy_consumption": 0.0, "task_completion_rate": 0.4339622641509434, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7980598851517637}, {"total_delay": 4.340425531914893, "energy_consumption": 0.0, "task_completion_rate": 0.43119266055045874, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7959294684104367}, {"total_delay": 4.25, "energy_consumption": 0.0, "task_completion_rate": 0.42857142857142855, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7953571428571428}, {"total_delay": 4.510204081632653, "energy_consumption": 0.0, "task_completion_rate": 0.4298245614035088, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7949075068623941}, {"total_delay": 4.74, "energy_consumption": 0.0, "task_completion_rate": 0.43103448275862066, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7945448275862068}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 0.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.6666666666666666}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.25, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9991666666666666}, {"total_delay": 0.2, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9993333333333334}, {"total_delay": 0.2, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9993333333333334}, {"total_delay": 0.8333333333333334, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9972222222222222}, {"total_delay": 0.875, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9970833333333333}, {"total_delay": 0.875, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9970833333333333}, {"total_delay": 0.7777777777777778, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9974074074074074}, {"total_delay": 0.7, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9976666666666666}, {"total_delay": 0.9090909090909091, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9969696969696971}, {"total_delay": 0.8333333333333334, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9715811965811966}, {"total_delay": 0.8571428571428571, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9749206349206349}, {"total_delay": 1.6, "energy_consumption": 0.0, "task_completion_rate": 0.8823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9554509803921568}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 0.8888888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.955462962962963}, {"total_delay": 2.823529411764706, "energy_consumption": 0.0, "task_completion_rate": 0.8947368421052632, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.955500515995872}, {"total_delay": 3.388888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.8571428571428571, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9410846560846561}, {"total_delay": 3.388888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.8181818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9280976430976432}, {"total_delay": 3.263157894736842, "energy_consumption": 0.0, "task_completion_rate": 0.7037037037037037, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8903573749187784}, {"total_delay": 3.2, "energy_consumption": 0.0, "task_completion_rate": 0.6896551724137931, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8858850574712643}, {"total_delay": 3.2, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8782222222222221}, {"total_delay": 3.0454545454545454, "energy_consumption": 0.0, "task_completion_rate": 0.6285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.866038961038961}, {"total_delay": 2.9130434782608696, "energy_consumption": 0.0, "task_completion_rate": 0.6388888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8699194847020933}, {"total_delay": 3.5833333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8547222222222222}, {"total_delay": 3.576923076923077, "energy_consumption": 0.0, "task_completion_rate": 0.6046511627906976, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.856293977340489}, {"total_delay": 4.185185185185185, "energy_consumption": 0.0, "task_completion_rate": 0.574468085106383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8442054110848437}, {"total_delay": 4.75, "energy_consumption": 0.0, "task_completion_rate": 0.5490196078431373, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8338398692810457}, {"total_delay": 4.75, "energy_consumption": 0.0, "task_completion_rate": 0.5283018867924528, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.826933962264151}, {"total_delay": 5.068965517241379, "energy_consumption": 0.0, "task_completion_rate": 0.5370370370370371, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8287824606215412}, {"total_delay": 5.258064516129032, "energy_consumption": 0.0, "task_completion_rate": 0.5254237288135594, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8242810278840897}, {"total_delay": 5.09375, "energy_consumption": 0.0, "task_completion_rate": 0.5245901639344263, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.824550887978142}, {"total_delay": 5.09375, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8163541666666667}, {"total_delay": 5.264705882352941, "energy_consumption": 0.0, "task_completion_rate": 0.5074626865671642, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.818271875914545}, {"total_delay": 5.542857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.5147058823529411, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8197591036414567}, {"total_delay": 5.542857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.5072463768115942, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8172726017943409}, {"total_delay": 5.416666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8152777777777778}, {"total_delay": 5.297297297297297, "energy_consumption": 0.0, "task_completion_rate": 0.49333333333333335, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8134534534534534}, {"total_delay": 5.297297297297297, "energy_consumption": 0.0, "task_completion_rate": 0.4805194805194805, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8091821691821691}, {"total_delay": 5.461538461538462, "energy_consumption": 0.0, "task_completion_rate": 0.48148148148148145, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8089553656220323}, {"total_delay": 5.7317073170731705, "energy_consumption": 0.0, "task_completion_rate": 0.4880952380952381, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8102593883081687}, {"total_delay": 6.023809523809524, "energy_consumption": 0.0, "task_completion_rate": 0.4827586206896552, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8075068418171867}, {"total_delay": 6.023809523809524, "energy_consumption": 0.0, "task_completion_rate": 0.47191011235955055, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8038906723738185}, {"total_delay": 6.431818181818182, "energy_consumption": 0.0, "task_completion_rate": 0.46808510638297873, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8012556415215989}, {"total_delay": 6.644444444444445, "energy_consumption": 0.0, "task_completion_rate": 0.47368421052631576, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8024132553606238}, {"total_delay": 6.826086956521739, "energy_consumption": 0.0, "task_completion_rate": 0.46464646464646464, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7987951983604158}, {"total_delay": 6.957446808510638, "energy_consumption": 0.0, "task_completion_rate": 0.46534653465346537, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7985906888561196}, {"total_delay": 7.0625, "energy_consumption": 0.0, "task_completion_rate": 0.46601941747572817, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7984648058252427}, {"total_delay": 7.183673469387755, "energy_consumption": 0.0, "task_completion_rate": 0.46226415094339623, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7968091387498396}, {"total_delay": 7.32, "energy_consumption": 0.0, "task_completion_rate": 0.46296296296296297, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7965876543209877}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.4, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9953333333333333}, {"total_delay": 1.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9955555555555556}, {"total_delay": 1.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9955555555555556}, {"total_delay": 1.375, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9954166666666667}, {"total_delay": 1.2222222222222223, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9959259259259259}, {"total_delay": 1.3, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9956666666666667}, {"total_delay": 1.4545454545454546, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9951515151515151}, {"total_delay": 1.5833333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9947222222222223}, {"total_delay": 1.4615384615384615, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9951282051282052}, {"total_delay": 1.4615384615384615, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9951282051282052}, {"total_delay": 1.4285714285714286, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9952380952380953}, {"total_delay": 1.4375, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9952083333333333}, {"total_delay": 1.4375, "energy_consumption": 0.0, "task_completion_rate": 0.8888888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9581712962962964}, {"total_delay": 1.411764705882353, "energy_consumption": 0.0, "task_completion_rate": 0.8947368421052632, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9602063983488133}, {"total_delay": 1.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9622222222222222}, {"total_delay": 1.4210526315789473, "energy_consumption": 0.0, "task_completion_rate": 0.9047619047619048, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9635171261487051}, {"total_delay": 1.4, "energy_consumption": 0.0, "task_completion_rate": 0.9090909090909091, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.965030303030303}, {"total_delay": 1.380952380952381, "energy_consumption": 0.0, "task_completion_rate": 0.84, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9420634920634919}, {"total_delay": 1.380952380952381, "energy_consumption": 0.0, "task_completion_rate": 0.8076923076923077, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9312942612942613}, {"total_delay": 2.0434782608695654, "energy_consumption": 0.0, "task_completion_rate": 0.8214285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9336645962732919}, {"total_delay": 1.92, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9380444444444445}, {"total_delay": 1.8846153846153846, "energy_consumption": 0.0, "task_completion_rate": 0.8387096774193549, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9399545078577337}, {"total_delay": 1.8888888888888888, "energy_consumption": 0.0, "task_completion_rate": 0.8181818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9330976430976431}, {"total_delay": 1.8888888888888888, "energy_consumption": 0.0, "task_completion_rate": 0.7714285714285715, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9175132275132275}, {"total_delay": 1.8275862068965518, "energy_consumption": 0.0, "task_completion_rate": 0.7631578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.914960677555959}, {"total_delay": 1.8, "energy_consumption": 0.0, "task_completion_rate": 0.7317073170731707, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9045691056910569}, {"total_delay": 1.8, "energy_consumption": 0.0, "task_completion_rate": 0.7142857142857143, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8987619047619048}, {"total_delay": 1.7741935483870968, "energy_consumption": 0.0, "task_completion_rate": 0.6888888888888889, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8903823178016727}, {"total_delay": 2.46875, "energy_consumption": 0.0, "task_completion_rate": 0.6808510638297872, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8853878546099291}, {"total_delay": 2.393939393939394, "energy_consumption": 0.0, "task_completion_rate": 0.673469387755102, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.883176664605236}, {"total_delay": 3.085714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.660377358490566, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8765067385444745}, {"total_delay": 3.0, "energy_consumption": 0.0, "task_completion_rate": 0.6428571428571429, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.870952380952381}, {"total_delay": 2.918918918918919, "energy_consumption": 0.0, "task_completion_rate": 0.6491228070175439, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8733112059427849}, {"total_delay": 3.4210526315789473, "energy_consumption": 0.0, "task_completion_rate": 0.6129032258064516, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.859564233163554}, {"total_delay": 3.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.582089552238806, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8495854063018241}, {"total_delay": 3.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.5571428571428572, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8412698412698413}, {"total_delay": 3.275, "energy_consumption": 0.0, "task_completion_rate": 0.547945205479452, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.838398401826484}, {"total_delay": 3.6904761904761907, "energy_consumption": 0.0, "task_completion_rate": 0.5454545454545454, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8361832611832613}, {"total_delay": 3.604651162790698, "energy_consumption": 0.0, "task_completion_rate": 0.5512820512820513, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8384118465513813}, {"total_delay": 3.522727272727273, "energy_consumption": 0.0, "task_completion_rate": 0.55, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8382575757575758}, {"total_delay": 3.522727272727273, "energy_consumption": 0.0, "task_completion_rate": 0.5365853658536586, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8337860310421287}, {"total_delay": 3.466666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.5172413793103449, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8275249042145595}, {"total_delay": 3.4130434782608696, "energy_consumption": 0.0, "task_completion_rate": 0.4946236559139785, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8201644070437899}, {"total_delay": 3.4130434782608696, "energy_consumption": 0.0, "task_completion_rate": 0.4842105263157895, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8166933638443936}, {"total_delay": 3.382978723404255, "energy_consumption": 0.0, "task_completion_rate": 0.47959183673469385, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8152540165002171}, {"total_delay": 3.3469387755102042, "energy_consumption": 0.0, "task_completion_rate": 0.47572815533980584, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.814086255861568}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.6, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9979999999999999}, {"total_delay": 1.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9955555555555556}, {"total_delay": 1.1428571428571428, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961904761904762}, {"total_delay": 1.375, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9954166666666667}, {"total_delay": 1.2, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.996}, {"total_delay": 1.0909090909090908, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9963636363636365}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9728571428571429}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 0.875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9550000000000001}, {"total_delay": 1.0666666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9408888888888889}, {"total_delay": 1.1875, "energy_consumption": 0.0, "task_completion_rate": 0.7272727272727273, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9051325757575758}, {"total_delay": 1.3529411764705883, "energy_consumption": 0.0, "task_completion_rate": 0.7391304347826086, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.908533674339301}, {"total_delay": 1.4444444444444444, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.884074074074074}, {"total_delay": 1.5263157894736843, "energy_consumption": 0.0, "task_completion_rate": 0.6551724137931034, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8799697519661223}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 0.6451612903225806, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8767204301075268}, {"total_delay": 2.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.65625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8779563492063492}, {"total_delay": 2.9545454545454546, "energy_consumption": 0.0, "task_completion_rate": 0.6285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8663419913419913}, {"total_delay": 2.8260869565217392, "energy_consumption": 0.0, "task_completion_rate": 0.6052631578947368, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8590007627765065}, {"total_delay": 2.7083333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8576388888888888}, {"total_delay": 3.36, "energy_consumption": 0.0, "task_completion_rate": 0.5952380952380952, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.853879365079365}, {"total_delay": 3.8461538461538463, "energy_consumption": 0.0, "task_completion_rate": 0.5777777777777777, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8464387464387464}, {"total_delay": 4.333333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.5869565217391305, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8478743961352656}, {"total_delay": 4.785714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.5833333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8451587301587301}, {"total_delay": 4.785714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.5714285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8411904761904762}, {"total_delay": 4.620689655172414, "energy_consumption": 0.0, "task_completion_rate": 0.58, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8445977011494253}, {"total_delay": 5.133333333333334, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8347407407407408}, {"total_delay": 4.967741935483871, "energy_consumption": 0.0, "task_completion_rate": 0.543859649122807, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8313940765893229}, {"total_delay": 5.363636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.532258064516129, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8262072336265884}, {"total_delay": 5.205882352941177, "energy_consumption": 0.0, "task_completion_rate": 0.5151515151515151, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8210308972073678}, {"total_delay": 5.085714285714285, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8163809523809524}, {"total_delay": 4.972222222222222, "energy_consumption": 0.0, "task_completion_rate": 0.4931506849315068, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8144761542364282}, {"total_delay": 4.972222222222222, "energy_consumption": 0.0, "task_completion_rate": 0.48, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8100925925925925}, {"total_delay": 5.315789473684211, "energy_consumption": 0.0, "task_completion_rate": 0.475, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.807280701754386}, {"total_delay": 5.315789473684211, "energy_consumption": 0.0, "task_completion_rate": 0.4523809523809524, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7997410192147033}, {"total_delay": 5.6, "energy_consumption": 0.0, "task_completion_rate": 0.449438202247191, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7978127340823971}, {"total_delay": 5.6, "energy_consumption": 0.0, "task_completion_rate": 0.43956043956043955, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7945201465201466}, {"total_delay": 5.809523809523809, "energy_consumption": 0.0, "task_completion_rate": 0.4421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.794670008354219}, {"total_delay": 5.674418604651163, "energy_consumption": 0.0, "task_completion_rate": 0.4387755102040816, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7940104413858565}, {"total_delay": 5.674418604651163, "energy_consumption": 0.0, "task_completion_rate": 0.42574257425742573, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.789666129403638}, {"total_delay": 5.568181818181818, "energy_consumption": 0.0, "task_completion_rate": 0.41904761904761906, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7877886002886004}, {"total_delay": 5.568181818181818, "energy_consumption": 0.0, "task_completion_rate": 0.411214953271028, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7851777116964033}, {"total_delay": 5.382978723404255, "energy_consumption": 0.0, "task_completion_rate": 0.41228070175438597, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7861503048401145}, {"total_delay": 5.3125, "energy_consumption": 0.0, "task_completion_rate": 0.41025641025641024, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.78571047008547}, {"total_delay": 5.3125, "energy_consumption": 0.0, "task_completion_rate": 0.39344262295081966, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7801058743169399}, {"total_delay": 5.285714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.3951612903225806, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7807680491551459}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.4, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9986666666666667}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.42857142857142855, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9985714285714286}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 1.2222222222222223, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9959259259259259}, {"total_delay": 1.1, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9963333333333333}, {"total_delay": 1.7272727272727273, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9942424242424241}, {"total_delay": 1.5833333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9947222222222223}, {"total_delay": 1.5384615384615385, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9948717948717949}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9727777777777779}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9727777777777779}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 0.9411764705882353, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9728921568627452}, {"total_delay": 2.1176470588235294, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9744226579520697}, {"total_delay": 2.1176470588235294, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9744226579520697}, {"total_delay": 2.0555555555555554, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9598148148148148}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 0.7916666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9238888888888889}, {"total_delay": 1.9523809523809523, "energy_consumption": 0.0, "task_completion_rate": 0.75, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9101587301587303}, {"total_delay": 1.9545454545454546, "energy_consumption": 0.0, "task_completion_rate": 0.7333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9045959595959596}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 0.71875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8995833333333333}, {"total_delay": 2.0416666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8820833333333334}, {"total_delay": 2.08, "energy_consumption": 0.0, "task_completion_rate": 0.6578947368421053, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8790315789473685}, {"total_delay": 2.076923076923077, "energy_consumption": 0.0, "task_completion_rate": 0.6341463414634146, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.871125703564728}, {"total_delay": 2.037037037037037, "energy_consumption": 0.0, "task_completion_rate": 0.574468085106383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8513659049120043}, {"total_delay": 2.037037037037037, "energy_consumption": 0.0, "task_completion_rate": 0.5510204081632653, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8435500125976315}, {"total_delay": 2.8275862068965516, "energy_consumption": 0.0, "task_completion_rate": 0.5686274509803921, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8467838629704755}, {"total_delay": 3.533333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8400740740740741}, {"total_delay": 3.4193548387096775, "energy_consumption": 0.0, "task_completion_rate": 0.5535714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8397926267281107}, {"total_delay": 3.3125, "energy_consumption": 0.0, "task_completion_rate": 0.5423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8364159604519775}, {"total_delay": 3.8181818181818183, "energy_consumption": 0.0, "task_completion_rate": 0.5409836065573771, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8342672627918529}, {"total_delay": 4.294117647058823, "energy_consumption": 0.0, "task_completion_rate": 0.5151515151515151, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8240701128936423}, {"total_delay": 4.171428571428572, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8194285714285714}, {"total_delay": 4.083333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8197222222222221}, {"total_delay": 4.083333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8197222222222221}, {"total_delay": 4.394736842105263, "energy_consumption": 0.0, "task_completion_rate": 0.5135135135135135, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8231887150308204}, {"total_delay": 4.794871794871795, "energy_consumption": 0.0, "task_completion_rate": 0.5064935064935064, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8195149295149294}, {"total_delay": 5.125, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.81625}, {"total_delay": 5.0, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8166666666666668}, {"total_delay": 5.357142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.5060240963855421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8174842226047044}, {"total_delay": 5.232558139534884, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8158914728682171}, {"total_delay": 5.232558139534884, "energy_consumption": 0.0, "task_completion_rate": 0.4942528735632184, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8139757640559565}, {"total_delay": 5.044444444444444, "energy_consumption": 0.0, "task_completion_rate": 0.5056179775280899, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8183911776945485}, {"total_delay": 5.434782608695652, "energy_consumption": 0.0, "task_completion_rate": 0.5054945054945055, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8170488931358496}, {"total_delay": 5.319148936170213, "energy_consumption": 0.0, "task_completion_rate": 0.5053763440860215, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.817394951574773}, {"total_delay": 5.208333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.48484848484848486, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8109217171717171}, {"total_delay": 5.612244897959184, "energy_consumption": 0.0, "task_completion_rate": 0.47115384615384615, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8050104657247514}, {"total_delay": 5.94, "energy_consumption": 0.0, "task_completion_rate": 0.46296296296296297, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8011876543209876}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.6666666666666666, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9977777777777778}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 1.1666666666666667, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961111111111111}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 0.8888888888888888, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.997037037037037}, {"total_delay": 1.4, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9953333333333333}, {"total_delay": 1.9090909090909092, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9936363636363637}, {"total_delay": 1.9090909090909092, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9936363636363637}, {"total_delay": 2.3076923076923075, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9684981684981685}, {"total_delay": 2.7857142857142856, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9684920634920635}, {"total_delay": 2.7857142857142856, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9684920634920635}, {"total_delay": 2.5, "energy_consumption": 0.0, "task_completion_rate": 0.9411764705882353, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9720588235294118}, {"total_delay": 3.0, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9714814814814815}, {"total_delay": 3.4444444444444446, "energy_consumption": 0.0, "task_completion_rate": 0.8571428571428571, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9408994708994709}, {"total_delay": 3.8947368421052633, "energy_consumption": 0.0, "task_completion_rate": 0.76, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9070175438596492}, {"total_delay": 4.3, "energy_consumption": 0.0, "task_completion_rate": 0.7407407407407407, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8992469135802469}, {"total_delay": 4.714285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.7, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8842857142857143}, {"total_delay": 4.714285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.7, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8842857142857143}, {"total_delay": 4.5, "energy_consumption": 0.0, "task_completion_rate": 0.6875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8808333333333334}, {"total_delay": 4.3478260869565215, "energy_consumption": 0.0, "task_completion_rate": 0.696969696969697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8844971453667106}, {"total_delay": 4.16, "energy_consumption": 0.0, "task_completion_rate": 0.6944444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8842814814814814}, {"total_delay": 4.038461538461538, "energy_consumption": 0.0, "task_completion_rate": 0.7027027027027027, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8874393624393625}, {"total_delay": 4.518518518518518, "energy_consumption": 0.0, "task_completion_rate": 0.7105263157894737, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8884470435347628}, {"total_delay": 5.0, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8722222222222222}, {"total_delay": 4.827586206896552, "energy_consumption": 0.0, "task_completion_rate": 0.6444444444444445, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.865389527458493}, {"total_delay": 4.827586206896552, "energy_consumption": 0.0, "task_completion_rate": 0.6170212765957447, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8562484715089264}, {"total_delay": 5.166666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8577777777777778}, {"total_delay": 4.90625, "energy_consumption": 0.0, "task_completion_rate": 0.6153846153846154, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.855440705128205}, {"total_delay": 5.2727272727272725, "energy_consumption": 0.0, "task_completion_rate": 0.6111111111111112, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8527946127946128}, {"total_delay": 5.617647058823529, "energy_consumption": 0.0, "task_completion_rate": 0.576271186440678, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8400315719508141}, {"total_delay": 5.9714285714285715, "energy_consumption": 0.0, "task_completion_rate": 0.5737704918032787, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8380187353629976}, {"total_delay": 5.9714285714285715, "energy_consumption": 0.0, "task_completion_rate": 0.5645161290322581, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8349339477726575}, {"total_delay": 6.333333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.5625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8330555555555555}, {"total_delay": 6.72972972972973, "energy_consumption": 0.0, "task_completion_rate": 0.5606060606060606, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.831102921102921}, {"total_delay": 7.026315789473684, "energy_consumption": 0.0, "task_completion_rate": 0.5428571428571428, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8241979949874687}, {"total_delay": 7.256410256410256, "energy_consumption": 0.0, "task_completion_rate": 0.5342465753424658, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8205608242594544}, {"total_delay": 7.256410256410256, "energy_consumption": 0.0, "task_completion_rate": 0.527027027027027, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8181543081543081}, {"total_delay": 7.619047619047619, "energy_consumption": 0.0, "task_completion_rate": 0.5316455696202531, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.818485031143259}, {"total_delay": 7.441860465116279, "energy_consumption": 0.0, "task_completion_rate": 0.5119047619047619, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8124953857511997}, {"total_delay": 7.681818181818182, "energy_consumption": 0.0, "task_completion_rate": 0.5057471264367817, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8096429815395333}, {"total_delay": 7.511111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.4945054945054945, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8064647944647945}, {"total_delay": 7.782608695652174, "energy_consumption": 0.0, "task_completion_rate": 0.4946236559139785, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8055991896524857}, {"total_delay": 8.063829787234043, "energy_consumption": 0.0, "task_completion_rate": 0.49473684210526314, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8046995147443076}, {"total_delay": 8.063829787234043, "energy_consumption": 0.0, "task_completion_rate": 0.49473684210526314, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8046995147443076}, {"total_delay": 7.916666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.48, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8002777777777778}, {"total_delay": 7.775510204081633, "energy_consumption": 0.0, "task_completion_rate": 0.48514851485148514, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8024644709368896}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 1.2, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.996}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 0.875, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9970833333333333}, {"total_delay": 0.875, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9970833333333333}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 2.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9916666666666667}, {"total_delay": 3.357142857142857, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9888095238095239}, {"total_delay": 3.357142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9665873015873016}, {"total_delay": 3.2, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9685}, {"total_delay": 2.8823529411764706, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9718736383442265}, {"total_delay": 2.8823529411764706, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9718736383442265}, {"total_delay": 3.263157894736842, "energy_consumption": 0.0, "task_completion_rate": 0.9047619047619048, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9573767752715122}, {"total_delay": 3.263157894736842, "energy_consumption": 0.0, "task_completion_rate": 0.8636363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9436682615629984}, {"total_delay": 3.6666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.8076923076923077, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9236752136752138}, {"total_delay": 4.181818181818182, "energy_consumption": 0.0, "task_completion_rate": 0.7586206896551724, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9056008359456635}, {"total_delay": 4.608695652173913, "energy_consumption": 0.0, "task_completion_rate": 0.696969696969697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8836275801493193}, {"total_delay": 4.608695652173913, "energy_consumption": 0.0, "task_completion_rate": 0.6388888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.86426731078905}, {"total_delay": 4.8, "energy_consumption": 0.0, "task_completion_rate": 0.6410256410256411, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8643418803418803}, {"total_delay": 4.8, "energy_consumption": 0.0, "task_completion_rate": 0.625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.859}, {"total_delay": 4.653846153846154, "energy_consumption": 0.0, "task_completion_rate": 0.6341463414634146, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8625359599749843}, {"total_delay": 4.592592592592593, "energy_consumption": 0.0, "task_completion_rate": 0.627906976744186, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8606603502727533}, {"total_delay": 4.592592592592593, "energy_consumption": 0.0, "task_completion_rate": 0.574468085106383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8428473863934857}, {"total_delay": 4.592592592592593, "energy_consumption": 0.0, "task_completion_rate": 0.5510204081632653, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8350314940791131}, {"total_delay": 4.433333333333334, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.837074074074074}, {"total_delay": 4.32258064516129, "energy_consumption": 0.0, "task_completion_rate": 0.5344827586206896, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8304189840563589}, {"total_delay": 4.787878787878788, "energy_consumption": 0.0, "task_completion_rate": 0.5238095238095238, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8253102453102453}, {"total_delay": 5.264705882352941, "energy_consumption": 0.0, "task_completion_rate": 0.5151515151515151, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8208348187759952}, {"total_delay": 5.628571428571429, "energy_consumption": 0.0, "task_completion_rate": 0.49295774647887325, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.812224010731053}, {"total_delay": 5.777777777777778, "energy_consumption": 0.0, "task_completion_rate": 0.4931506849315068, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.811790969051243}, {"total_delay": 5.777777777777778, "energy_consumption": 0.0, "task_completion_rate": 0.47368421052631576, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8053021442495126}, {"total_delay": 5.526315789473684, "energy_consumption": 0.0, "task_completion_rate": 0.475, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8065789473684211}, {"total_delay": 5.410256410256411, "energy_consumption": 0.0, "task_completion_rate": 0.4642857142857143, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8033943833943834}, {"total_delay": 5.3, "energy_consumption": 0.0, "task_completion_rate": 0.47058823529411764, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8058627450980392}, {"total_delay": 5.195121951219512, "energy_consumption": 0.0, "task_completion_rate": 0.45555555555555555, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8012014453477868}, {"total_delay": 5.476190476190476, "energy_consumption": 0.0, "task_completion_rate": 0.46153846153846156, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8022588522588522}, {"total_delay": 5.744186046511628, "energy_consumption": 0.0, "task_completion_rate": 0.4574468085106383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8000016493485074}, {"total_delay": 5.9772727272727275, "energy_consumption": 0.0, "task_completion_rate": 0.4583333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.799520202020202}, {"total_delay": 5.9772727272727275, "energy_consumption": 0.0, "task_completion_rate": 0.4489795918367347, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7964022881880025}, {"total_delay": 6.108695652173913, "energy_consumption": 0.0, "task_completion_rate": 0.45098039215686275, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7966311452117077}, {"total_delay": 6.340425531914893, "energy_consumption": 0.0, "task_completion_rate": 0.4392523364485981, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7919493603764831}, {"total_delay": 6.583333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.42857142857142855, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.787579365079365}, {"total_delay": 6.755102040816326, "energy_consumption": 0.0, "task_completion_rate": 0.4298245614035088, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7874245136651151}, {"total_delay": 6.92, "energy_consumption": 0.0, "task_completion_rate": 0.43103448275862066, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7872781609195402}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9941666666666666}, {"total_delay": 1.6666666666666667, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9944444444444445}, {"total_delay": 1.6, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9946666666666667}, {"total_delay": 1.5454545454545454, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9948484848484848}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.5384615384615385, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9710622710622712}, {"total_delay": 1.4285714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9730158730158731}, {"total_delay": 1.4285714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9535714285714286}, {"total_delay": 2.75, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.938201754385965}, {"total_delay": 3.2941176470588234, "energy_consumption": 0.0, "task_completion_rate": 0.7727272727272727, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9132620320855614}, {"total_delay": 3.7777777777777777, "energy_consumption": 0.0, "task_completion_rate": 0.782608695652174, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9149436392914655}, {"total_delay": 4.2105263157894735, "energy_consumption": 0.0, "task_completion_rate": 0.7916666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9165204678362574}, {"total_delay": 4.65, "energy_consumption": 0.0, "task_completion_rate": 0.7407407407407407, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8980802469135801}, {"total_delay": 4.428571428571429, "energy_consumption": 0.0, "task_completion_rate": 0.6774193548387096, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8777112135176651}, {"total_delay": 4.863636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.6470588235294118, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8661408199643494}, {"total_delay": 4.6521739130434785, "energy_consumption": 0.0, "task_completion_rate": 0.6216216216216216, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8583666274970622}, {"total_delay": 5.0, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.85}, {"total_delay": 4.8, "energy_consumption": 0.0, "task_completion_rate": 0.5952380952380952, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8490793650793651}, {"total_delay": 5.1923076923076925, "energy_consumption": 0.0, "task_completion_rate": 0.6046511627906976, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8509093619558735}, {"total_delay": 5.555555555555555, "energy_consumption": 0.0, "task_completion_rate": 0.6136363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8526936026936026}, {"total_delay": 5.892857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.5833333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.841468253968254}, {"total_delay": 6.172413793103448, "energy_consumption": 0.0, "task_completion_rate": 0.5918367346938775, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8433708655876143}, {"total_delay": 6.466666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.5769230769230769, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8374188034188034}, {"total_delay": 6.258064516129032, "energy_consumption": 0.0, "task_completion_rate": 0.5849056603773585, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8407750050720226}, {"total_delay": 6.0625, "energy_consumption": 0.0, "task_completion_rate": 0.5925925925925926, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8439891975308642}, {"total_delay": 6.0625, "energy_consumption": 0.0, "task_completion_rate": 0.5714285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8369345238095237}, {"total_delay": 6.294117647058823, "energy_consumption": 0.0, "task_completion_rate": 0.5666666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8345751633986929}, {"total_delay": 6.6, "energy_consumption": 0.0, "task_completion_rate": 0.5737704918032787, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8359234972677596}, {"total_delay": 6.416666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.5625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8327777777777777}, {"total_delay": 6.243243243243243, "energy_consumption": 0.0, "task_completion_rate": 0.5362318840579711, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8245998172085128}, {"total_delay": 6.243243243243243, "energy_consumption": 0.0, "task_completion_rate": 0.5138888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8171521521521522}, {"total_delay": 6.631578947368421, "energy_consumption": 0.0, "task_completion_rate": 0.5135135135135135, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.815732574679943}, {"total_delay": 6.35, "energy_consumption": 0.0, "task_completion_rate": 0.5263157894736842, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.820938596491228}, {"total_delay": 6.195121951219512, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8126829268292682}, {"total_delay": 6.071428571428571, "energy_consumption": 0.0, "task_completion_rate": 0.4772727272727273, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8055194805194805}, {"total_delay": 6.348837209302325, "energy_consumption": 0.0, "task_completion_rate": 0.4725274725274725, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8030130334781497}, {"total_delay": 6.613636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.46808510638297873, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8006495809155384}, {"total_delay": 6.613636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.4631578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7990071770334928}, {"total_delay": 6.369565217391305, "energy_consumption": 0.0, "task_completion_rate": 0.46464646464646464, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8003169374908504}, {"total_delay": 6.369565217391305, "energy_consumption": 0.0, "task_completion_rate": 0.46, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7987681159420289}, {"total_delay": 6.25531914893617, "energy_consumption": 0.0, "task_completion_rate": 0.4519230769230769, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.796456628477905}, {"total_delay": 6.25531914893617, "energy_consumption": 0.0, "task_completion_rate": 0.4519230769230769, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.796456628477905}, {"total_delay": 6.061224489795919, "energy_consumption": 0.0, "task_completion_rate": 0.44545454545454544, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7949474335188621}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 0.6, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9979999999999999}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.5714285714285714, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.998095238095238}, {"total_delay": 0.5714285714285714, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.998095238095238}, {"total_delay": 0.875, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9970833333333333}, {"total_delay": 1.3, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9956666666666667}, {"total_delay": 1.3, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9956666666666667}, {"total_delay": 1.9090909090909092, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9936363636363637}, {"total_delay": 2.4615384615384617, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9917948717948718}, {"total_delay": 2.857142857142857, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9904761904761905}, {"total_delay": 2.857142857142857, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9904761904761905}, {"total_delay": 2.857142857142857, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9904761904761905}, {"total_delay": 2.8, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9698333333333333}, {"total_delay": 2.6470588235294117, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9726579520697167}, {"total_delay": 2.7222222222222223, "energy_consumption": 0.0, "task_completion_rate": 0.9473684210526315, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9733820662768031}, {"total_delay": 2.7, "energy_consumption": 0.0, "task_completion_rate": 0.9090909090909091, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9606969696969697}, {"total_delay": 2.7142857142857144, "energy_consumption": 0.0, "task_completion_rate": 0.875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9492857142857143}, {"total_delay": 2.7142857142857144, "energy_consumption": 0.0, "task_completion_rate": 0.84, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9376190476190476}, {"total_delay": 2.7142857142857144, "energy_consumption": 0.0, "task_completion_rate": 0.84, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9376190476190476}, {"total_delay": 2.5833333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.8, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9247222222222223}, {"total_delay": 2.56, "energy_consumption": 0.0, "task_completion_rate": 0.7352941176470589, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9032313725490196}, {"total_delay": 2.576923076923077, "energy_consumption": 0.0, "task_completion_rate": 0.7027027027027027, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8923111573111573}, {"total_delay": 2.5555555555555554, "energy_consumption": 0.0, "task_completion_rate": 0.675, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8831481481481482}, {"total_delay": 2.5357142857142856, "energy_consumption": 0.0, "task_completion_rate": 0.6511627906976745, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8752685492801772}, {"total_delay": 2.4827586206896552, "energy_consumption": 0.0, "task_completion_rate": 0.6304347826086957, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8685357321339331}, {"total_delay": 2.466666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8584444444444445}, {"total_delay": 2.4838709677419355, "energy_consumption": 0.0, "task_completion_rate": 0.5961538461538461, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8571050454921423}, {"total_delay": 2.46875, "energy_consumption": 0.0, "task_completion_rate": 0.5714285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8489136904761905}, {"total_delay": 2.46875, "energy_consumption": 0.0, "task_completion_rate": 0.5614035087719298, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8455720029239767}, {"total_delay": 2.911764705882353, "energy_consumption": 0.0, "task_completion_rate": 0.5666666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8458496732026144}, {"total_delay": 2.8285714285714287, "energy_consumption": 0.0, "task_completion_rate": 0.5737704918032787, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8484949258391881}, {"total_delay": 3.1944444444444446, "energy_consumption": 0.0, "task_completion_rate": 0.5714285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.846494708994709}, {"total_delay": 3.108108108108108, "energy_consumption": 0.0, "task_completion_rate": 0.5692307692307692, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.846049896049896}, {"total_delay": 3.4473684210526314, "energy_consumption": 0.0, "task_completion_rate": 0.5507246376811594, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8387503178235444}, {"total_delay": 3.769230769230769, "energy_consumption": 0.0, "task_completion_rate": 0.5571428571428572, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8398168498168498}, {"total_delay": 4.075, "energy_consumption": 0.0, "task_completion_rate": 0.5405405405405406, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8332635135135136}, {"total_delay": 4.390243902439025, "energy_consumption": 0.0, "task_completion_rate": 0.5324675324675324, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8295216978143807}, {"total_delay": 4.690476190476191, "energy_consumption": 0.0, "task_completion_rate": 0.5316455696202531, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8282469359051637}, {"total_delay": 4.5813953488372094, "energy_consumption": 0.0, "task_completion_rate": 0.524390243902439, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.826192096804689}, {"total_delay": 4.909090909090909, "energy_consumption": 0.0, "task_completion_rate": 0.5238095238095238, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8249062049062049}, {"total_delay": 4.8, "energy_consumption": 0.0, "task_completion_rate": 0.5113636363636364, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.821121212121212}, {"total_delay": 5.130434782608695, "energy_consumption": 0.0, "task_completion_rate": 0.5168539325842697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8218498615860609}, {"total_delay": 5.0212765957446805, "energy_consumption": 0.0, "task_completion_rate": 0.4895833333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8131235224586288}, {"total_delay": 5.354166666666667, "energy_consumption": 0.0, "task_completion_rate": 0.48484848484848486, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.810435606060606}, {"total_delay": 5.6938775510204085, "energy_consumption": 0.0, "task_completion_rate": 0.48514851485148514, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8094032464470936}, {"total_delay": 5.58, "energy_consumption": 0.0, "task_completion_rate": 0.4854368932038835, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8098789644012946}], "task_completion_rates": [0.45045045045045046, 0.43103448275862066, 0.46296296296296297, 0.47572815533980584, 0.3951612903225806, 0.46296296296296297, 0.48514851485148514, 0.43103448275862066, 0.44545454545454544, 0.4854368932038835], "average_delays": [4.82, 4.74, 7.32, 3.3469387755102042, 5.285714285714286, 5.94, 7.775510204081633, 6.92, 6.061224489795919, 5.58]}, "eval_stats": {"mean_reward": -5443.456011171955, "std_reward": 1045.3031904980428, "mean_length": 50.0, "mean_completion_rate": 0.45253747410659184, "mean_delay": 5.778938775510204}}