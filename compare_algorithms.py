"""
PPO vs MAPPO 对比训练脚本
车联网边缘计算场景下的算法性能对比
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import torch
from typing import Dict, List

from config.config import *
from env.mdp_environment import VehicularEdgeComputingEnv
from algorithms.ppo import PPOAgent, PPOTrainer
from algorithms.simple_mappo import train_simple_mappo


def setup_comparison_directories():
    """设置对比实验目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_dir = f"results/comparison_{timestamp}"

    dirs = {
        'base': base_dir,
        'ppo': f"{base_dir}/ppo",
        'mappo': f"{base_dir}/mappo",
        'comparison': f"{base_dir}/comparison"
    }

    for dir_path in dirs.values():
        os.makedirs(dir_path, exist_ok=True)
        os.makedirs(f"{dir_path}/models", exist_ok=True)
        os.makedirs(f"{dir_path}/plots", exist_ok=True)

    return dirs


def train_ppo(env, config_dict: Dict, save_dir: str) -> Dict:
    """训练PPO算法"""
    print("=== 开始PPO训练 ===")

    # 获取状态和动作空间信息
    state_dim = env.observation_space.shape[0]
    action_space = env.action_space

    # 创建PPO智能体和训练器
    ppo_config = config_dict['ppo']
    agent = PPOAgent(state_dim, action_space, ppo_config)
    trainer = PPOTrainer(env, agent, ppo_config)

    # 训练
    training_results = trainer.train(ppo_config.total_timesteps)

    # 保存模型
    model_path = os.path.join(save_dir, "models", "ppo_model.pt")
    agent.save(model_path)

    # 评估
    eval_results, eval_stats = evaluate_agent(env, agent, num_episodes=20)

    return {
        'training_results': training_results,
        'eval_results': eval_results,
        'eval_stats': eval_stats,
        'algorithm': 'PPO'
    }


def train_mappo(env, config_dict: Dict, save_dir: str) -> Dict:
    """训练MAPPO算法"""
    return train_simple_mappo(env, config_dict, save_dir)


def evaluate_agent(env, agent, num_episodes: int = 10):
    """评估PPO智能体性能"""
    eval_results = {
        'episode_rewards': [],
        'episode_lengths': [],
        'performance_metrics': [],
        'task_completion_rates': [],
        'average_delays': []
    }

    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False

        while not done:
            action, _, _ = agent.get_action(state, deterministic=True)
            state, reward, done, info = env.step(action.squeeze())

            episode_reward += reward
            episode_length += 1

            if 'metrics' in info:
                eval_results['performance_metrics'].append(info['metrics'])

        eval_results['episode_rewards'].append(episode_reward)
        eval_results['episode_lengths'].append(episode_length)

        # 提取任务完成率和平均延迟
        if eval_results['performance_metrics']:
            last_metrics = eval_results['performance_metrics'][-1]
            eval_results['task_completion_rates'].append(
                last_metrics.get('task_completion_rate', 0)
            )
            eval_results['average_delays'].append(
                last_metrics.get('total_delay', 0)
            )

    # 计算统计数据
    stats = {
        'mean_reward': np.mean(eval_results['episode_rewards']),
        'std_reward': np.std(eval_results['episode_rewards']),
        'mean_length': np.mean(eval_results['episode_lengths']),
        'mean_completion_rate': np.mean(eval_results['task_completion_rates']),
        'mean_delay': np.mean(eval_results['average_delays'])
    }

    return eval_results, stats





def plot_comparison_results(results: Dict, save_dir: str):
    """绘制对比结果"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    algorithms = ['PPO', 'MAPPO']
    colors = ['blue', 'red']

    # 1. 训练奖励对比
    for i, (alg, color) in enumerate(zip(algorithms, colors)):
        alg_key = alg.lower()
        if alg_key in results:
            rewards = results[alg_key]['training_results']['episode_rewards']
            episodes = range(len(rewards))

            # 计算滑动平均
            window_size = min(50, len(rewards) // 10)
            if window_size > 1:
                moving_avg = []
                for j in range(len(rewards)):
                    start_idx = max(0, j - window_size // 2)
                    end_idx = min(len(rewards), j + window_size // 2 + 1)
                    moving_avg.append(np.mean(rewards[start_idx:end_idx]))
                axes[0, 0].plot(episodes, moving_avg, color=color, label=f'{alg} (Moving Avg)', linewidth=2)
            else:
                axes[0, 0].plot(episodes, rewards, color=color, label=alg, linewidth=2)

    axes[0, 0].set_title('Training Rewards Comparison')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].legend()
    axes[0, 0].grid(True)

    # 2. 评估性能对比
    metrics = ['mean_reward', 'mean_completion_rate', 'mean_delay']
    metric_names = ['Mean Reward', 'Completion Rate', 'Mean Delay']

    for idx, (metric, name) in enumerate(zip(metrics, metric_names)):
        ax = axes[0, idx + 1] if idx < 2 else axes[1, 0]

        values = []
        errors = []
        labels = []

        for alg in algorithms:
            alg_key = alg.lower()
            if alg_key in results:
                if metric == 'mean_reward':
                    values.append(results[alg_key]['eval_stats'][metric])
                    errors.append(results[alg_key]['eval_stats']['std_reward'])
                else:
                    values.append(results[alg_key]['eval_stats'][metric])
                    errors.append(0)  # 简化，不显示其他指标的误差
                labels.append(alg)

        bars = ax.bar(labels, values, yerr=errors, capsize=5, color=colors[:len(labels)], alpha=0.7)
        ax.set_title(f'{name} Comparison')
        ax.set_ylabel(name)
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + (errors[bars.index(bar)] if errors[bars.index(bar)] > 0 else 0),
                   f'{value:.3f}', ha='center', va='bottom')

    # 3. 性能指标时间序列对比
    for i, (alg, color) in enumerate(zip(algorithms, colors)):
        alg_key = alg.lower()
        if alg_key in results and results[alg_key]['eval_results']['performance_metrics']:
            metrics_data = results[alg_key]['eval_results']['performance_metrics']
            completion_rates = [m.get('task_completion_rate', 0) for m in metrics_data]
            timesteps = range(len(completion_rates))

            axes[1, 1].plot(timesteps, completion_rates, color=color, label=f'{alg} Completion Rate', alpha=0.7)

    axes[1, 1].set_title('Task Completion Rate Over Time')
    axes[1, 1].set_xlabel('Timestep')
    axes[1, 1].set_ylabel('Completion Rate')
    axes[1, 1].legend()
    axes[1, 1].grid(True)

    # 4. 综合性能雷达图
    categories = ['Reward', 'Completion Rate', 'Low Delay', 'Stability']

    # 归一化性能指标
    ppo_scores = []
    mappo_scores = []

    if 'ppo' in results and 'mappo' in results:
        # 奖励 (归一化到0-1)
        ppo_reward = results['ppo']['eval_stats']['mean_reward']
        mappo_reward = results['mappo']['eval_stats']['mean_reward']
        max_reward = max(ppo_reward, mappo_reward)
        min_reward = min(ppo_reward, mappo_reward)
        if max_reward != min_reward:
            ppo_scores.append((ppo_reward - min_reward) / (max_reward - min_reward))
            mappo_scores.append((mappo_reward - min_reward) / (max_reward - min_reward))
        else:
            ppo_scores.append(0.5)
            mappo_scores.append(0.5)

        # 完成率
        ppo_scores.append(results['ppo']['eval_stats']['mean_completion_rate'])
        mappo_scores.append(results['mappo']['eval_stats']['mean_completion_rate'])

        # 低延迟 (延迟越低越好，所以取倒数)
        ppo_delay = results['ppo']['eval_stats']['mean_delay']
        mappo_delay = results['mappo']['eval_stats']['mean_delay']
        max_delay = max(ppo_delay, mappo_delay)
        ppo_scores.append(1 - ppo_delay / max_delay if max_delay > 0 else 0.5)
        mappo_scores.append(1 - mappo_delay / max_delay if max_delay > 0 else 0.5)

        # 稳定性 (方差越小越好)
        ppo_stability = 1 / (1 + results['ppo']['eval_stats']['std_reward'])
        mappo_stability = 1 / (1 + results['mappo']['eval_stats']['std_reward'])
        max_stability = max(ppo_stability, mappo_stability)
        ppo_scores.append(ppo_stability / max_stability)
        mappo_scores.append(mappo_stability / max_stability)

        # 绘制雷达图
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合

        ppo_scores += ppo_scores[:1]
        mappo_scores += mappo_scores[:1]

        ax = axes[1, 2]
        ax.plot(angles, ppo_scores, 'o-', linewidth=2, label='PPO', color='blue')
        ax.fill(angles, ppo_scores, alpha=0.25, color='blue')
        ax.plot(angles, mappo_scores, 'o-', linewidth=2, label='MAPPO', color='red')
        ax.fill(angles, mappo_scores, alpha=0.25, color='red')

        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_title('Overall Performance Comparison')
        ax.legend()
        ax.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'algorithm_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()


def main():
    """主函数"""
    print("=== PPO vs MAPPO 算法对比实验 ===")

    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)

    # 创建配置
    system_config = SystemConfig()
    task_config = TaskConfig()
    ppo_config = PPOConfig()

    # 设置保存目录
    dirs = setup_comparison_directories()
    print(f"结果保存路径: {dirs['base']}")

    # 保存配置
    config_dict = {
        'system': system_config,
        'task': task_config,
        'ppo': ppo_config
    }

    # 创建环境
    print("创建仿真环境...")
    env = VehicularEdgeComputingEnv(system_config)

    # 存储结果
    results = {}

    # 训练PPO
    try:
        ppo_results = train_ppo(env, config_dict, dirs['ppo'])
        results['ppo'] = ppo_results
        print("PPO训练完成!")

        # 保存PPO结果
        with open(os.path.join(dirs['ppo'], 'results.json'), 'w') as f:
            json.dump({k: v for k, v in ppo_results.items() if k != 'training_results'}, f, indent=2)

    except Exception as e:
        print(f"PPO训练失败: {e}")

    # 训练MAPPO
    try:
        mappo_results = train_mappo(env, config_dict, dirs['mappo'])
        results['mappo'] = mappo_results
        print("MAPPO训练完成!")

        # 保存MAPPO结果
        with open(os.path.join(dirs['mappo'], 'results.json'), 'w') as f:
            json.dump({k: v for k, v in mappo_results.items() if k != 'training_results'}, f, indent=2)

    except Exception as e:
        print(f"MAPPO训练失败: {e}")

    # 生成对比图表
    if len(results) >= 2:
        print("生成对比图表...")
        plot_comparison_results(results, dirs['comparison'])

        # 打印对比结果
        print("\n=== 算法性能对比 ===")
        for alg_name, alg_results in results.items():
            stats = alg_results['eval_stats']
            print(f"\n{alg_name.upper()}:")
            print(f"  平均奖励: {stats['mean_reward']:.3f} ± {stats['std_reward']:.3f}")
            print(f"  任务完成率: {stats['mean_completion_rate']:.3f}")
            print(f"  平均延迟: {stats['mean_delay']:.3f}")

        # 保存完整对比结果
        with open(os.path.join(dirs['comparison'], 'comparison_results.json'), 'w') as f:
            json.dump({k: {kk: vv for kk, vv in v.items() if kk != 'training_results'}
                      for k, v in results.items()}, f, indent=2)

    print(f"\n对比实验完成! 所有结果保存在: {dirs['base']}")


if __name__ == "__main__":
    main()
