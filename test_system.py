"""
系统测试脚本
验证车联网边缘计算仿真系统的基本功能
"""

import os
import numpy as np
import torch
from datetime import datetime

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        from config.config import SystemConfig, TaskConfig, PPOConfig
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from env.mdp_environment import VehicularEdgeComputingEnv
        print("✓ 环境模块导入成功")
    except Exception as e:
        print(f"✗ 环境模块导入失败: {e}")
        return False
    
    try:
        from algorithms.ppo import PPOAgent, PPOTrainer
        print("✓ PPO算法模块导入成功")
    except Exception as e:
        print(f"✗ PPO算法模块导入失败: {e}")
        return False
    
    try:
        from algorithms.simple_mappo import train_simple_mappo
        print("✓ 简化MAPPO模块导入成功")
    except Exception as e:
        print(f"✗ 简化MAPPO模块导入失败: {e}")
        return False
    
    return True


def test_environment():
    """测试环境创建和基本功能"""
    print("\n=== 测试环境功能 ===")
    
    try:
        from config.config import SystemConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        
        # 创建配置
        config = SystemConfig()
        print("✓ 配置创建成功")
        
        # 创建环境
        env = VehicularEdgeComputingEnv(config)
        print("✓ 环境创建成功")
        
        # 测试环境重置
        state = env.reset()
        print(f"✓ 环境重置成功，状态维度: {state.shape}")
        
        # 测试动作空间
        action_space = env.action_space
        print(f"✓ 动作空间: {action_space}")
        
        # 测试随机动作
        action = action_space.sample()
        print(f"✓ 随机动作采样: {action}")
        
        # 测试环境步进
        next_state, reward, done, info = env.step(action)
        print(f"✓ 环境步进成功，奖励: {reward:.3f}, 完成: {done}")
        
        return True
        
    except Exception as e:
        print(f"✗ 环境测试失败: {e}")
        return False


def test_ppo_agent():
    """测试PPO智能体"""
    print("\n=== 测试PPO智能体 ===")
    
    try:
        from config.config import SystemConfig, PPOConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        from algorithms.ppo import PPOAgent
        
        # 创建环境
        system_config = SystemConfig()
        env = VehicularEdgeComputingEnv(system_config)
        
        # 获取状态和动作空间
        state_dim = env.observation_space.shape[0]
        action_space = env.action_space
        
        # 创建PPO配置
        ppo_config = PPOConfig()
        print("✓ PPO配置创建成功")
        
        # 创建PPO智能体
        agent = PPOAgent(state_dim, action_space, ppo_config)
        print("✓ PPO智能体创建成功")
        
        # 测试动作获取
        state = env.reset()
        action, logprob, value = agent.get_action(state)
        print(f"✓ 动作获取成功，动作: {action}, 价值: {value}")
        
        # 测试经验存储
        agent.store_transition(state, action.squeeze(), logprob.item(), 1.0, value.item(), False)
        print("✓ 经验存储成功")
        
        return True
        
    except Exception as e:
        print(f"✗ PPO智能体测试失败: {e}")
        return False


def test_short_training():
    """测试短期训练"""
    print("\n=== 测试短期训练 ===")
    
    try:
        from config.config import SystemConfig, PPOConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        from algorithms.ppo import PPOAgent, PPOTrainer
        
        # 创建配置
        system_config = SystemConfig()
        ppo_config = PPOConfig()
        
        # 设置短期训练参数
        ppo_config.total_timesteps = 100  # 只训练100步
        ppo_config.steps_per_update = 20   # 每20步更新一次
        ppo_config.n_epochs = 2           # 减少更新轮数
        
        # 创建环境和智能体
        env = VehicularEdgeComputingEnv(system_config)
        state_dim = env.observation_space.shape[0]
        action_space = env.action_space
        
        agent = PPOAgent(state_dim, action_space, ppo_config)
        trainer = PPOTrainer(env, agent, ppo_config)
        
        print("✓ 训练器创建成功")
        
        # 开始短期训练
        print("开始短期训练...")
        training_results = trainer.train(ppo_config.total_timesteps)
        
        print(f"✓ 短期训练完成")
        print(f"  - 总episodes: {len(training_results['episode_rewards'])}")
        print(f"  - 平均奖励: {np.mean(training_results['episode_rewards']):.3f}")
        print(f"  - 平均长度: {np.mean(training_results['episode_lengths']):.1f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 短期训练测试失败: {e}")
        return False


def test_simple_mappo():
    """测试简化MAPPO"""
    print("\n=== 测试简化MAPPO ===")
    
    try:
        from config.config import SystemConfig, PPOConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        from algorithms.simple_mappo import SimpleMAPPOAgent, SimpleMAPPOConfig
        
        # 创建配置
        system_config = SystemConfig()
        mappo_config = SimpleMAPPOConfig()
        
        # 创建环境
        env = VehicularEdgeComputingEnv(system_config)
        state_dim = env.observation_space.shape[0]
        action_space = env.action_space
        
        # 创建简化MAPPO智能体
        agent = SimpleMAPPOAgent('test_agent', state_dim, action_space, mappo_config)
        print("✓ 简化MAPPO智能体创建成功")
        
        # 测试动作获取
        state = env.reset()
        action, logprob, value = agent.get_action(state)
        print(f"✓ 动作获取成功，动作: {action}")
        
        return True
        
    except Exception as e:
        print(f"✗ 简化MAPPO测试失败: {e}")
        return False


def test_reward_normalization():
    """测试奖励归一化"""
    print("\n=== 测试奖励归一化 ===")
    
    try:
        from config.config import SystemConfig
        from env.mdp_environment import VehicularEdgeComputingEnv
        
        # 创建环境
        system_config = SystemConfig()
        env = VehicularEdgeComputingEnv(system_config)
        
        # 运行几步，收集奖励
        rewards = []
        state = env.reset()
        
        for _ in range(10):
            action = env.action_space.sample()
            state, reward, done, info = env.step(action)
            rewards.append(reward)
            
            if done:
                state = env.reset()
        
        print(f"✓ 奖励收集成功")
        print(f"  - 奖励范围: [{min(rewards):.3f}, {max(rewards):.3f}]")
        print(f"  - 平均奖励: {np.mean(rewards):.3f}")
        print(f"  - 奖励标准差: {np.std(rewards):.3f}")
        
        # 检查奖励是否在合理范围内
        if all(-2 <= r <= 2 for r in rewards):
            print("✓ 奖励归一化正常")
        else:
            print("⚠ 奖励可能未正确归一化")
        
        return True
        
    except Exception as e:
        print(f"✗ 奖励归一化测试失败: {e}")
        return False


def test_visualization():
    """测试可视化功能"""
    print("\n=== 测试可视化功能 ===")
    
    try:
        import matplotlib.pyplot as plt
        
        # 创建测试数据
        episodes = list(range(100))
        rewards = [np.random.normal(-0.5, 0.2) for _ in episodes]
        
        # 测试基本绘图
        fig, ax = plt.subplots(1, 1, figsize=(8, 6))
        ax.plot(episodes, rewards, label='Test Rewards')
        ax.set_title('Test Visualization')
        ax.set_xlabel('Episode')
        ax.set_ylabel('Reward')
        ax.legend()
        ax.grid(True)
        
        # 保存测试图片
        test_dir = "test_results"
        os.makedirs(test_dir, exist_ok=True)
        plt.savefig(f"{test_dir}/test_plot.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 可视化功能正常")
        print(f"  - 测试图片保存至: {test_dir}/test_plot.png")
        
        return True
        
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 车联网边缘计算仿真系统测试")
    print("=" * 50)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("环境功能", test_environment),
        ("PPO智能体", test_ppo_agent),
        ("短期训练", test_short_training),
        ("简化MAPPO", test_simple_mappo),
        ("奖励归一化", test_reward_normalization),
        ("可视化功能", test_visualization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
        
        print("-" * 30)
    
    # 输出测试结果
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        print("\n💡 建议下一步:")
        print("  1. 运行 python main.py 进行完整训练")
        print("  2. 运行 python compare_algorithms.py 进行算法对比")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
