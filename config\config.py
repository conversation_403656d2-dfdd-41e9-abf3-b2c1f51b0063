"""
配置参数
定义仿真环境的所有超参数和系统参数
"""

import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass, field

@dataclass
class NetworkConfig:
    """网络配置"""
    # 最大重传次数
    max_retransmissions: int = 3

    # 路由参数
    routing_protocol: str = "AODV"  # 默认路由协议
    hello_interval: int = 1  # Hello报文间隔时间(秒)
    route_timeout: int = 10  # 路由超时时间(秒)

@dataclass
class QueueConfig:
    """队列系统配置"""
    max_lifetime: int = 20  # 任务最大生命周期 (时隙)
    num_priorities: int = 3  # 优先级数量

@dataclass
class SystemConfig:
    """系统基础配置"""
    # 时间参数
    delta_t: float = 0.1  # 时隙持续时间(秒)
    max_time_slots: int = 50  # 最大仿真时隙数 - 减少episode长度加速训练

    # 节点数量 - 减少节点数量加速训练
    num_vehicles: int = 10
    num_rsus: int = 4
    num_uavs: int = 2

    # 区域参数
    area_width: float = 2000.0  # 仿真区域宽度(米)
    area_height: float = 2000.0  # 仿真区域高度(米)

@dataclass
class TaskConfig:
    """任务配置"""
    # 任务生成参数
    task_arrival_rate: float = 0.3  # 降低任务到达率(tasks/时隙)
    data_size_range: Tuple[float, float] = (0.5e6, 5e6)  # 减小数据大小范围(bits)
    computation_density: float = 800  # 降低处理密度(cycles/bit)
    output_ratio: float = 0.1  # 输出结果与输入比例
    max_tolerance_range: Tuple[int, int] = (8, 25)  # 增加容忍延迟范围(时隙)

    # 优先级参数
    num_priorities: int = 3  # 优先级数量
    priority_weights: List[float] = None  # 优先级权重

    # 生命周期参数
    max_lifetime: int = 20  # 最大生命周期(时隙)

    def __post_init__(self):
        if self.priority_weights is None:
            self.priority_weights = [0.3, 0.5, 0.2]  # 高、中、低优先级比例

@dataclass
class VehicleConfig:
    """车辆配置"""
    # 计算能力
    compute_capacity_range: Tuple[float, float] = (1e9, 3e9)  # CPU频率范围(cycles/s)

    # 传输参数
    tx_power: float = 23.0  # 传输功率(dBm)
    max_bandwidth: float = 20e6  # 最大带宽(Hz)
      # 移动参数
    speed_range: Tuple[float, float] = (10.0, 30.0)  # 速度范围(m/s)

    # 能耗参数
    max_energy: float = 10000.0  # 最大电量(J)
    kappa_comp: float = 1e-28  # 计算能耗系数

@dataclass
class RSUConfig:
    """RSU配置"""
    # 计算能力
    compute_capacity_range: Tuple[float, float] = (5e9, 10e9)  # CPU频率范围
    compute_capacity: float = 8e9  # 固定计算能力

    # 缓存参数
    cache_capacity_range: Tuple[float, float] = (100e6, 500e6)  # 缓存容量范围(bits)
    cache_capacity: float = 300e6  # 固定缓存容量

    # 传输参数
    tx_power: float = 30.0  # 传输功率(dBm)
    max_bandwidth: float = 50e6  # 最大带宽(Hz)
    coverage_radius: float = 300.0  # 覆盖半径(米)
    height: float = 30.0  # RSU高度(米)

    # 能耗参数
    kappa_comp: float = 1e-28  # 计算能耗系数

@dataclass
class UAVConfig:
    """UAV配置"""
    # 计算能力
    compute_capacity_range: Tuple[float, float] = (2e9, 5e9)  # CPU频率范围
    compute_capacity: float = 3e9  # 固定计算能力

    # 飞行参数
    max_speed: float = 50.0  # 最大速度(m/s)
    max_velocity: float = 50.0  # 最大速度(m/s) - 别名
    flying_height: float = 100.0  # 飞行高度(米)
    hovering_height: float = 100.0  # 悬停高度(米)
    coverage_radius: float = 500.0  # 覆盖半径(米)

    # 能量参数
    max_energy: float = 1000.0  # 最大电池容量(焦耳)

    # 传输参数
    tx_power: float = 26.0  # 传输功率(dBm)
    rx_power: float = 1.0  # 接收功率(瓦特)
    max_bandwidth: float = 30e6  # 最大带宽(Hz)

    # 飞行功耗参数 (基于论文公式)
    P0: float = 79.8563  # 叶型功率系数
    Pi: float = 88.6279  # 诱导功率系数
    Utip: float = 120.0  # 桨叶尖端速度(m/s)
    v0: float = 4.03  # 悬停时平均诱导速度(m/s)
    d0: float = 0.6  # 机身阻力系数
    rho_air: float = 1.225  # 空气密度(kg/m^3)
    s: float = 0.05  # 桨盘固体度
    A: float = 0.503  # 桨盘面积(m^2)

@dataclass
class CommunicationConfig:
    """通信配置"""
    # 信道参数
    noise_power_density: float = -174.0  # 噪声功率谱密度(dBm/Hz)
    path_loss_exponent_los: float = 2.0  # LoS路径损耗指数
    path_loss_exponent_nlos: float = 3.5  # NLoS路径损耗指数

    # 阴影衰落
    shadow_std_los: float = 3.0  # LoS阴影衰落标准差(dB)
    shadow_std_nlos: float = 8.0  # NLoS阴影衰落标准差(dB)

    # SINR阈值
    sinr_threshold: float = -10.0  # 最小SINR阈值(dB)

@dataclass
class OptimizationConfig:
    """优化配置"""
    # 目标函数权重
    omega_T: float = 1.0  # 延迟权重
    omega_E: float = 0.01  # 能耗权重
    omega_L: float = 10.0  # 数据丢失权重

    # 奖励函数权重 (用于强化学习环境)
    delay_weight: float = 0.5  # 降低延迟惩罚权重
    energy_weight: float = 0.01  # 能耗惩罚权重
    failure_penalty: float = 8.0  # 降低任务失败惩罚
    completion_reward: float = 10.0  # 增加任务完成奖励

    # 任务分类阈值
    tau1: int = 3  # 极度延迟敏感阈值(时隙)
    tau2: int = 8  # 延迟敏感阈值(时隙)
    tau3: int = 15  # 中度延迟容忍阈值(时隙)

@dataclass
class PPOConfig:
    """PPO算法配置"""
    # 网络参数 - 简化网络加速训练
    hidden_dim: int = 128
    num_layers: int = 2

    # 训练参数
    learning_rate: float = 3e-4
    lr_decay_steps: int = 10000  # 学习率衰减步数
    lr_decay_rate: float = 0.8    # 学习率衰减率
    batch_size: int = 32
    n_epochs: int = 5  # 减少epoch数加速训练
    update_epochs: int = 5  # 别名，与n_epochs保持一致
    clip_range: float = 0.2
    clip_epsilon: float = 0.2  # 别名，与clip_range保持一致
    gamma: float = 0.99
    gae_lambda: float = 0.95
    steps_per_update: int = 256  # 每次更新的步数 - 适应较短的episode

    # 探索参数
    entropy_coef: float = 0.01
    value_loss_coef: float = 0.5
    max_grad_norm: float = 0.5

    # 训练设置
    total_timesteps: int = 10000  # 减少总训练步数，快速测试
    eval_freq: int = 2000  # 减少评估频率
    save_freq: int = 5000  # 减少保存频率

class Config:
    """主配置类"""
    def __init__(self):
        self.system = SystemConfig()
        self.task = TaskConfig()
        self.vehicle = VehicleConfig()
        self.rsu = RSUConfig()
        self.uav = UAVConfig()
        self.communication = CommunicationConfig()
        self.optimization = OptimizationConfig()
        self.ppo = PPOConfig()

        # 验证配置
        self._validate_config()

    def _validate_config(self):
        """验证配置参数的合理性"""
        assert self.task.max_lifetime <= self.system.max_time_slots
        assert len(self.task.priority_weights) == self.task.num_priorities
        assert abs(sum(self.task.priority_weights) - 1.0) < 1e-6
        assert self.optimization.tau1 < self.optimization.tau2 < self.optimization.tau3

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'system': self.system.__dict__,
            'task': self.task.__dict__,
            'vehicle': self.vehicle.__dict__,
            'rsu': self.rsu.__dict__,
            'uav': self.uav.__dict__,
            'communication': self.communication.__dict__,
            'optimization': self.optimization.__dict__,
            'ppo': self.ppo.__dict__
        }
