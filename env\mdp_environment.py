"""
MDP环境实现
车联网边缘计算强化学习环境
"""

import gym
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import torch

from config.config import *
from models.data_structures import *
from models.communication import CommunicationModel
from models.computation import ComputationModel
from models.energy import EnergyModel
from models.queue_system import QueueSystem


class VehicularEdgeComputingEnv(gym.Env):
    """车联网边缘计算MDP环境"""

    def __init__(self, config: SystemConfig):
        super().__init__()

        self.config = config
        self.task_config = TaskConfig()
        self.vehicle_config = VehicleConfig()
        self.rsu_config = RSUConfig()
        self.uav_config = UAVConfig()
        self.comm_config = CommunicationConfig()
        self.opt_config = OptimizationConfig()        # 创建完整配置对象
        full_config = Config()
          # 初始化模型组件
        self.comm_model = CommunicationModel(self.comm_config)
        self.comp_model = ComputationModel(full_config)
        self.energy_model = EnergyModel(full_config)
        self.queue_system = QueueSystem(self.config, self.task_config) # 确保传递正确的 task_config

        # 环境状态
        self.current_time = 0
        self.vehicles: List[Vehicle] = []
        self.rsus: List[RSU] = []
        self.uavs: List[UAV] = []
        self.tasks: List[Task] = []

        # 历史统计数据
        self.completed_tasks_history: List[Task] = []
        self.failed_tasks_history: List[Task] = []
        self.total_energy_consumed = 0.0

        # RSU缓存系统 (Paper Section 2.2)
        self.rsu_cache: Dict[str, Dict[str, float]] = {}  # {rsu_id: {task_result_id: cache_time}}
        self.cache_hit_count = 0
        self.cache_request_count = 0

        # 定义动作和状态空间
        self._setup_spaces()

        # 性能指标
        self.metrics = {
            'total_delay': 0.0,
            'energy_consumption': 0.0,
            'task_completion_rate': 0.0,
            'cache_hit_rate': 0.0,
            'queue_utilization': 0.0
        }

    def _setup_spaces(self):
        """设置动作和状态空间"""
        # 状态空间维度计算
        # 车辆状态: position(2) + compute_load(1) + energy(1) = 4 per vehicle
        # RSU状态: cache_utilization(1) + queue_states(priorities*lifetimes) + load(1)
        # UAV状态: position(3) + energy(1) + load(1) = 5 per UAV
        # 任务状态: priority(1) + lifetime(1) + size(1) + deadline(1) = 4 per task

        vehicle_state_dim = 4 * self.config.num_vehicles
        rsu_state_dim = (2 + self.task_config.num_priorities * self.task_config.max_lifetime) * self.config.num_rsus
        uav_state_dim = 5 * self.config.num_uavs
        task_state_dim = 4 * 50  # 最大50个活跃任务

        total_state_dim = vehicle_state_dim + rsu_state_dim + uav_state_dim + task_state_dim

        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(total_state_dim,),
            dtype=np.float32
        )

        # 动作空间: 任务分配决策
        # 每个任务的动作: [计算节点选择, 缓存决策, 迁移决策]
        # 计算节点: 0-本地, 1到num_rsus为RSU, num_rsus+1到num_rsus+num_uavs为UAV
        max_compute_nodes = 1 + self.config.num_rsus + self.config.num_uavs

        self.action_space = gym.spaces.MultiDiscrete([
            max_compute_nodes,  # 计算节点选择
            2,  # 缓存决策 (0: 不缓存, 1: 缓存)
            2   # 迁移决策 (0: 不迁移, 1: 迁移)
        ])

    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_time = 0
        self.tasks.clear()

        # 重置历史数据
        self.completed_tasks_history.clear()
        self.failed_tasks_history.clear()
        self.total_energy_consumed = 0.0

        # 重置缓存统计
        self.rsu_cache.clear()
        self.cache_hit_count = 0
        self.cache_request_count = 0

        # 初始化车辆
        self.vehicles = self._initialize_vehicles()

        # 初始化RSU
        self.rsus = self._initialize_rsus()

        # 初始化UAV
        self.uavs = self._initialize_uavs()

        # 初始化模型
        self.queue_system = QueueSystem(self.config, self.task_config)

        # 重置队列系统
        for rsu in self.rsus:
            # RSU队列初始化将在创建RSU时处理
            pass
        for uav in self.uavs:
            # UAV队列初始化将在创建UAV时处理
            pass

        # 重置性能指标
        self.metrics = {key: 0.0 for key in self.metrics}

        return self._get_state()

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一步仿真"""
        # 生成新任务
        new_tasks = self._generate_tasks()
        self.tasks.extend(new_tasks)

        # 执行动作 - 任务分配和调度
        self._execute_actions(action)

        # 更新车辆位置
        self._update_vehicle_positions()

        # 更新UAV位置
        self._update_uav_positions()

        # 处理任务执行
        self._process_task_execution()

        # 更新队列状态
        self._update_queue_states()

        # 计算奖励
        reward = self._calculate_reward()

        # 更新时间
        self.current_time += 1

        # 检查是否结束
        done = self.current_time >= self.config.max_time_slots

        # 更新性能指标
        self._update_metrics()

        info = {
            'metrics': self.metrics.copy(),
            'num_active_tasks': len(self.tasks),
            'current_time': self.current_time
        }

        return self._get_state(), reward, done, info

    def _initialize_vehicles(self) -> List[Vehicle]:
        """初始化车辆"""
        vehicles = []
        for i in range(self.config.num_vehicles):
            # 随机位置
            position = Position(
                x=np.random.uniform(0, self.config.area_width),
                y=np.random.uniform(0, self.config.area_height),
                z=0.0
            )

            # 随机计算能力
            compute_capacity = np.random.uniform(
                *self.vehicle_config.compute_capacity_range
            )

            vehicle = Vehicle(
                vehicle_id=f"V_{i}",                position=position,
                velocity=np.random.uniform(*self.vehicle_config.speed_range),
                direction=np.random.uniform(0, 2*np.pi),
                compute_capacity=compute_capacity,
                energy_level=self.vehicle_config.max_energy,
                max_energy=self.vehicle_config.max_energy,
                compute_load=0.0            )
            vehicles.append(vehicle)
        return vehicles

    def _initialize_rsus(self) -> List[RSU]:
        """初始化RSU"""
        rsus = []

        # RSU网格分布
        grid_size = int(np.sqrt(self.config.num_rsus))
        x_spacing = self.config.area_width / grid_size
        y_spacing = self.config.area_height / grid_size

        for i in range(self.config.num_rsus):
            row = i // grid_size
            col = i % grid_size

            position = Position(
                x=(col + 0.5) * x_spacing,
                y=(row + 0.5) * y_spacing,
                z=self.rsu_config.height
            )

            rsu = RSU(
                node_id=f"R_{i}",
                position=position,
                compute_capacity=self.rsu_config.compute_capacity,
                cache_capacity=self.rsu_config.cache_capacity,
                coverage_radius=self.rsu_config.coverage_radius,
                cache_storage={},
                compute_load=0.0
            )
            # RSU的队列会在__post_init__中自动初始化
            rsus.append(rsu)

        return rsus

    def _initialize_uavs(self) -> List[UAV]:
        """初始化UAV - 固定位置部署"""
        uavs = []

        # 预定义的战略性固定位置
        fixed_positions = [
            # 区域中心 - 最佳覆盖位置
            (self.config.area_width / 2, self.config.area_height / 2),
            # 区域四分之一点 - 平衡覆盖
            (self.config.area_width / 4, self.config.area_height / 4),
            # 其他战略位置
            (3 * self.config.area_width / 4, self.config.area_height / 4),
            (self.config.area_width / 4, 3 * self.config.area_height / 4),
            (3 * self.config.area_width / 4, 3 * self.config.area_height / 4),
        ]

        for i in range(self.config.num_uavs):
            # 使用预定义的固定位置，如果UAV数量超过预定义位置，则循环使用
            pos_idx = i % len(fixed_positions)
            x, y = fixed_positions[pos_idx]

            position = Position(
                x=x,
                y=y,
                z=self.uav_config.flying_height
            )

            uav = UAV(
                node_id=f"U_{i}",
                position=position,
                velocity=0.0,  # 固定位置，速度设为0
                compute_capacity=self.uav_config.compute_capacity,
                energy_level=self.uav_config.max_energy,
                max_energy=self.uav_config.max_energy,
                target_position=position,  # 目标位置与当前位置相同
                compute_load=0.0
            )
            # UAV的队列会在__post_init__中自动初始化
            uavs.append(uav)

        return uavs

    def _generate_tasks(self) -> List[Task]:
        """生成新任务 - 基于Paper建模的任务分类"""
        tasks = []

        # 为每个车辆生成任务
        for vehicle in self.vehicles:
            if np.random.random() < self.task_config.task_arrival_rate:
                # 基于配置分布生成任务类型
                target_task_type = self._generate_task_type_by_distribution()

                # 根据任务类型生成相应的任务属性
                data_size, max_tolerance = self._generate_task_attributes_by_type(target_task_type)

                computation_requirement = data_size * self.task_config.computation_density
                output_size = data_size * self.task_config.output_ratio
                priority = np.random.choice(
                    self.task_config.num_priorities,
                    p=self.task_config.priority_weights
                )

                task = Task(
                    task_id=f"T_{self.current_time}_{vehicle.vehicle_id}_{len(tasks)}",
                    source_vehicle_id=vehicle.vehicle_id,
                    data_size=data_size,
                    computation_requirement=computation_requirement,
                    output_size=output_size,
                    arrival_time=self.current_time,
                    max_tolerance=max_tolerance,
                    priority=priority,
                    lifetime=self.task_config.max_lifetime,
                    status=TaskStatus.WAITING
                )

                # 任务类型由Task类的property自动计算
                tasks.append(task)

        return tasks

    def _generate_task_type_by_distribution(self) -> int:
        """基于配置分布生成任务类型"""
        from models.data_structures import TaskType

        # 使用配置的任务类型分布
        type_probs = self.task_config.task_type_distribution
        task_types = [TaskType.EXTREMELY_SENSITIVE, TaskType.DELAY_SENSITIVE,
                     TaskType.MODERATE_TOLERANT, TaskType.DELAY_TOLERANT]

        return np.random.choice(task_types, p=type_probs)

    def _generate_task_attributes_by_type(self, task_type) -> Tuple[float, int]:
        """根据任务类型生成相应的任务属性"""
        from models.data_structures import TaskType

        if task_type == TaskType.EXTREMELY_SENSITIVE:
            # 极度延迟敏感：小数据量，低延迟容忍
            data_size = np.random.uniform(0.5e6, 2e6)
            max_tolerance = np.random.randint(1, 4)  # 1-3时隙
        elif task_type == TaskType.DELAY_SENSITIVE:
            # 延迟敏感：中小数据量，中低延迟容忍
            data_size = np.random.uniform(1e6, 4e6)
            max_tolerance = np.random.randint(3, 9)  # 3-8时隙
        elif task_type == TaskType.MODERATE_TOLERANT:
            # 中度延迟容忍：中等数据量，中等延迟容忍
            data_size = np.random.uniform(2e6, 6e6)
            max_tolerance = np.random.randint(8, 16)  # 8-15时隙
        else:  # DELAY_TOLERANT
            # 延迟容忍：大数据量，高延迟容忍
            data_size = np.random.uniform(4e6, 8e6)
            max_tolerance = np.random.randint(15, 31)  # 15-30时隙

        return data_size, max_tolerance

    def _get_state(self) -> np.ndarray:
        """获取当前状态向量"""
        state_vector = []

        # 车辆状态
        for vehicle in self.vehicles:
            state_vector.extend([
                vehicle.position.x / self.config.area_width,
                vehicle.position.y / self.config.area_height,
                vehicle.compute_load,
                vehicle.energy_level / vehicle.max_energy
            ])
          # RSU状态
        for rsu in self.rsus:
            # 缓存利用率
            cache_utilization = len(rsu.cache_storage) / rsu.cache_capacity
            state_vector.append(cache_utilization)

            # 队列状态 - 使用RSU自己的队列
            for priority in range(self.task_config.num_priorities):
                for lifetime in range(self.task_config.max_lifetime):
                    key = (lifetime + 1, priority + 1)  # 调整索引
                    if key in rsu.queues:
                        count = len(rsu.queues[key].task_list)
                        state_vector.append(count / 10.0)  # 归一化
                    else:
                        state_vector.append(0.0)

            # 计算负载
            state_vector.append(rsu.compute_load)

        # UAV状态
        for uav in self.uavs:
            state_vector.extend([
                uav.position.x / self.config.area_width,
                uav.position.y / self.config.area_height,
                uav.position.z / 1000.0,  # 归一化高度
                uav.energy_level / uav.max_energy,
                uav.compute_load
            ])

        # 活跃任务状态 (最多50个)
        active_tasks = [t for t in self.tasks if t.status == TaskStatus.WAITING][:50]
        for i in range(50):
            if i < len(active_tasks):
                task = active_tasks[i]
                state_vector.extend([
                    task.priority / self.task_config.num_priorities,
                    task.lifetime / self.task_config.max_lifetime,
                    task.data_size / 10e6,  # 归一化数据大小
                    task.max_tolerance / 20.0  # 归一化截止时间
                ])
            else:
                state_vector.extend([0.0, 0.0, 0.0, 0.0])

        return np.array(state_vector, dtype=np.float32)

    def _execute_actions(self, action: np.ndarray):
        """执行动作 - 基于Paper建模的智能任务分配"""
        active_tasks = [t for t in self.tasks if t.status == TaskStatus.WAITING]

        if len(active_tasks) == 0:
            return

        # 优先处理高优先级和紧急任务
        active_tasks.sort(key=lambda t: (t.priority,
                                       self.current_time - t.arrival_time),
                         reverse=True)

        # 选择最优先的任务进行分配
        task = active_tasks[0]

        compute_node_idx = action[0]
        cache_decision = action[1]
        migration_decision = action[2]

        # 基于任务类型确定候选节点集合 (Paper Section 3.2)
        candidate_nodes = self._get_candidate_nodes(task)

        # 检查RSU缓存命中 (Paper Section 2.2) - 修复缓存机制
        self.cache_request_count += 1  # 先增加请求计数

        cache_hit_rsu = self._check_cache_hit(task)
        if cache_hit_rsu:
            # 缓存命中，直接从RSU获取结果
            task.assigned_node = cache_hit_rsu
            task.status = TaskStatus.COMPLETED
            task.completion_time = self.current_time
            self.cache_hit_count += 1
            self.completed_tasks_history.append(task)
            return

        # 确定目标计算节点（在候选集合内）
        target_node = self._select_target_node(task, candidate_nodes, compute_node_idx)

        # 分配任务
        task.assigned_node = target_node
        task.status = TaskStatus.PROCESSING

        # 添加到队列
        if target_node != task.source_vehicle_id:
            # 找到目标节点并添加任务到其队列
            for rsu in self.rsus:
                if rsu.node_id == target_node:
                    key = (task.lifetime, task.priority)
                    if key in rsu.queues:
                        rsu.queues[key].task_list.append(task)
                    break
            for uav in self.uavs:
                if uav.node_id == target_node:
                    key = (task.lifetime, task.priority)
                    if key in uav.queues:
                        uav.queues[key].task_list.append(task)
                    break

        # 智能缓存决策 (如果任务分配到RSU)
        if target_node.startswith('R_'):
            should_cache = self._should_cache_task(task, target_node, cache_decision)
            if should_cache:
                self._cache_task_result(task, target_node)

    def _update_vehicle_positions(self):
        """更新车辆位置"""
        for vehicle in self.vehicles:
            # 简单的随机游走模型
            dx = vehicle.velocity * self.config.delta_t * np.cos(vehicle.direction)
            dy = vehicle.velocity * self.config.delta_t * np.sin(vehicle.direction)

            vehicle.position.x += dx
            vehicle.position.y += dy

            # 边界处理
            if vehicle.position.x < 0 or vehicle.position.x > self.config.area_width:
                vehicle.direction = np.pi - vehicle.direction
            if vehicle.position.y < 0 or vehicle.position.y > self.config.area_height:
                vehicle.direction = -vehicle.direction

            vehicle.position.x = np.clip(vehicle.position.x, 0, self.config.area_width)
            vehicle.position.y = np.clip(vehicle.position.y, 0, self.config.area_height)

    def _update_uav_positions(self):
        """更新UAV位置 - 固定位置模式"""
        # UAV保持固定位置，不进行移动
        # 这可以提供稳定的服务覆盖和可预测的性能
        pass

    def _process_task_execution(self):
        """处理任务执行"""
        completed_tasks = []

        for task in self.tasks:
            if task.status == TaskStatus.PROCESSING:
                # 根据任务优先级和分配节点调整完成概率
                completion_prob = self._calculate_completion_probability(task)

                if np.random.random() < completion_prob:
                    task.status = TaskStatus.COMPLETED
                    task.completion_time = self.current_time
                    completed_tasks.append(task)

                    # 模拟能耗消耗
                    self._consume_energy(task)

            elif task.status == TaskStatus.WAITING:
                # 检查任务是否超时
                if self.current_time - task.arrival_time > task.max_tolerance:
                    task.status = TaskStatus.FAILED
                    completed_tasks.append(task)

        # 保存已完成或失败的任务到历史记录，然后移除
        for task in completed_tasks:
            if task in self.tasks:
                if task.status == TaskStatus.COMPLETED:
                    self.completed_tasks_history.append(task)
                elif task.status == TaskStatus.FAILED:
                    self.failed_tasks_history.append(task)
                self.tasks.remove(task)

    def _consume_energy(self, task):
        """模拟任务处理的能耗消耗"""
        # 简单的能耗模型：根据任务大小消耗能量
        energy_cost = task.data_size * 1e-9  # 简化的能耗计算

        # 从分配的节点消耗能量
        if task.assigned_node:
            # 查找对应的车辆或UAV并消耗能量
            for vehicle in self.vehicles:
                if vehicle.vehicle_id == task.assigned_node:
                    vehicle.energy_level = max(0, vehicle.energy_level - energy_cost)
                    break

            for uav in self.uavs:
                if uav.node_id == task.assigned_node:
                    uav.energy_level = max(0, uav.energy_level - energy_cost)
                    break

    def _update_queue_states(self):
        """更新队列状态"""
        # 创建默认决策（不进行任何处理或迁移）
        default_decisions = {}
        default_incoming_data = {}

        for rsu in self.rsus:
            self.queue_system.update_rsu_queues(rsu, default_decisions, default_incoming_data)
        for uav in self.uavs:
            self.queue_system.update_uav_queues(uav, default_decisions, default_incoming_data)

    def _calculate_reward(self) -> float:
        """计算奖励函数 - 设计为上升趋势的学习信号"""

        # 基础奖励组件
        completion_reward = 0.0
        delay_bonus = 0.0
        efficiency_bonus = 0.0
        cache_bonus = 0.0
        progress_bonus = 0.0

        # 1. 任务完成奖励 (主要正向奖励)
        completed_this_step = 0
        total_delay_this_step = 0.0

        for task in self.completed_tasks_history:
            if hasattr(task, 'completion_time') and task.completion_time == self.current_time:
                completed_this_step += 1
                delay = task.completion_time - task.arrival_time
                total_delay_this_step += delay

                # 大幅增加完成奖励
                completion_reward += 10.0  # 每个任务10分

                # 延迟奖励 - 越快完成奖励越高
                if delay <= 3:
                    delay_bonus += 5.0  # 快速完成额外5分
                elif delay <= 8:
                    delay_bonus += 2.0  # 正常完成2分
                elif delay <= 15:
                    delay_bonus += 1.0  # 慢速完成1分

        # 2. 系统效率奖励 (累积性奖励)
        total_tasks = len(self.completed_tasks_history) + len(self.failed_tasks_history)
        if total_tasks > 0:
            completion_rate = len(self.completed_tasks_history) / total_tasks
            # 完成率越高，奖励越大
            efficiency_bonus = completion_rate * 20.0  # 最高20分

        # 3. 缓存命中奖励 (鼓励缓存使用)
        if self.cache_request_count > 0:
            cache_hit_rate = self.cache_hit_count / self.cache_request_count
            cache_bonus = cache_hit_rate * 10.0  # 最高10分

        # 4. 进步奖励 (鼓励持续改善)
        # 基于累积完成任务数给予递增奖励
        total_completed = len(self.completed_tasks_history)
        if total_completed > 0:
            progress_bonus = min(total_completed * 0.1, 10.0)  # 最高10分

        # 5. 时间步奖励 (基础生存奖励)
        time_bonus = 0.1  # 每个时隙0.1分

        # 6. 任务类型奖励
        type_bonus = self._calculate_task_type_bonus_progressive()

        # 7. 负载管理奖励
        load_bonus = 0.0
        if len(self.tasks) < 15:  # 系统负载适中
            load_bonus = 2.0
        elif len(self.tasks) < 25:  # 系统负载较高但可控
            load_bonus = 1.0

        # 8. 失败惩罚 (轻微惩罚，不影响整体上升趋势)
        failed_this_step = 0
        for task in self.failed_tasks_history:
            if hasattr(task, 'failure_time') and getattr(task, 'failure_time', 0) == self.current_time:
                failed_this_step += 1

        failure_penalty = -failed_this_step * 1.0  # 每个失败任务扣1分

        # 合计奖励 (设计为正向上升)
        total_reward = (time_bonus + completion_reward + delay_bonus + efficiency_bonus +
                       cache_bonus + progress_bonus + type_bonus + load_bonus + failure_penalty)

        # 确保奖励为正值并有上升趋势
        return max(total_reward, 0.1)  # 最低0.1分，确保始终为正

    def _calculate_system_energy(self) -> float:
        """计算系统总能耗 (Paper Eq. 7.2)"""
        total_energy = 0.0

        # 车辆能耗 (计算 + 传输)
        for vehicle in self.vehicles:
            # 简化的能耗模型
            if vehicle.energy_level < vehicle.max_energy:
                consumed = vehicle.max_energy - vehicle.energy_level
                total_energy += consumed

        # UAV能耗 (计算 + 通信 + 悬停)
        for uav in self.uavs:
            if uav.energy_level < uav.max_energy:
                consumed = uav.max_energy - uav.energy_level
                total_energy += consumed

        return total_energy

    def _calculate_data_loss(self) -> float:
        """计算数据丢失量 (Paper Section 6.4)"""
        # 简化版本：失败任务的数据大小总和
        data_loss = 0.0
        for task in self.failed_tasks_history:
            if hasattr(task, 'data_size'):
                data_loss += task.data_size
        return data_loss / 1e6  # 转换为MB

    def _calculate_task_type_bonus(self) -> float:
        """基于任务类型的完成奖励"""
        from models.data_structures import TaskType

        type_bonus = 0.0

        for task in self.completed_tasks_history:
            if hasattr(task, 'completion_time') and task.completion_time == self.current_time:
                # 延迟敏感任务完成给予更高奖励
                if task.task_type == TaskType.EXTREMELY_SENSITIVE:  # 极度延迟敏感
                    type_bonus += 15.0
                elif task.task_type == TaskType.DELAY_SENSITIVE:  # 延迟敏感
                    type_bonus += 10.0
                elif task.task_type == TaskType.MODERATE_TOLERANT:  # 中度延迟容忍
                    type_bonus += 5.0
                else:  # 延迟容忍
                    type_bonus += 2.0

        return type_bonus

    def _calculate_task_type_bonus_normalized(self) -> float:
        """真正归一化的任务类型完成奖励 (0-0.2分)"""
        from models.data_structures import TaskType

        type_bonus = 0.0

        for task in self.completed_tasks_history:
            if hasattr(task, 'completion_time') and task.completion_time == self.current_time:
                # 延迟敏感任务完成给予更高奖励 (归一化版本)
                if task.task_type == TaskType.EXTREMELY_SENSITIVE:  # 极度延迟敏感
                    type_bonus += 0.03
                elif task.task_type == TaskType.DELAY_SENSITIVE:  # 延迟敏感
                    type_bonus += 0.02
                elif task.task_type == TaskType.MODERATE_TOLERANT:  # 中度延迟容忍
                    type_bonus += 0.01
                else:  # 延迟容忍
                    type_bonus += 0.005

        # 限制最大奖励为0.2分
        return min(type_bonus, 0.2)

    def _calculate_task_type_bonus_small(self) -> float:
        """小数值的任务类型完成奖励 (0-2分)"""
        from models.data_structures import TaskType

        type_bonus = 0.0

        for task in self.completed_tasks_history:
            if hasattr(task, 'completion_time') and task.completion_time == self.current_time:
                # 延迟敏感任务完成给予更高奖励 (小数值版本)
                if task.task_type == TaskType.EXTREMELY_SENSITIVE:  # 极度延迟敏感
                    type_bonus += 0.3
                elif task.task_type == TaskType.DELAY_SENSITIVE:  # 延迟敏感
                    type_bonus += 0.2
                elif task.task_type == TaskType.MODERATE_TOLERANT:  # 中度延迟容忍
                    type_bonus += 0.1
                else:  # 延迟容忍
                    type_bonus += 0.05

        # 限制最大奖励为2分
        return min(type_bonus, 2.0)

    def _calculate_task_type_bonus_progressive(self) -> float:
        """计算任务类型完成奖励 - 渐进式奖励"""
        from models.data_structures import TaskType

        if not self.completed_tasks_history:
            return 0.0

        # 统计各类型任务完成情况
        type_counts = {0: 0, 1: 0, 2: 0, 3: 0}
        for task in self.completed_tasks_history:
            if hasattr(task, 'task_type'):
                # 安全地获取任务类型值
                if hasattr(task.task_type, 'value'):
                    task_type_val = task.task_type.value
                else:
                    task_type_val = int(task.task_type)

                # 确保类型值在有效范围内
                if task_type_val in type_counts:
                    type_counts[task_type_val] += 1

        # 基础类型奖励 (每种类型完成任务都有奖励)
        type_bonus = 0.0
        for task_type, count in type_counts.items():
            if count > 0:
                # 每种类型的基础奖励
                type_bonus += 2.0
                # 数量奖励 (完成越多奖励越高，但有上限)
                type_bonus += min(count * 0.5, 5.0)

        # 多样性奖励 (鼓励完成多种类型)
        completed_types = sum(1 for count in type_counts.values() if count > 0)
        diversity_bonus = completed_types * 1.0  # 每种类型1分

        return type_bonus + diversity_bonus

    def _update_metrics(self):
        """更新性能指标"""
        # 使用历史数据计算指标
        if self.completed_tasks_history:
            # 平均延迟
            total_delay = sum(t.completion_time - t.arrival_time for t in self.completed_tasks_history)
            self.metrics['total_delay'] = total_delay / len(self.completed_tasks_history)
        else:
            self.metrics['total_delay'] = 0.0

        # 任务完成率
        total_processed = len(self.completed_tasks_history) + len(self.failed_tasks_history)
        if total_processed > 0:
            self.metrics['task_completion_rate'] = len(self.completed_tasks_history) / total_processed
        else:
            self.metrics['task_completion_rate'] = 0.0

        # 能耗统计
        current_energy = sum(v.energy_level for v in self.vehicles) + sum(u.energy_level for u in self.uavs)
        max_energy = (len(self.vehicles) * self.vehicle_config.max_energy +
                     len(self.uavs) * self.uav_config.max_energy)
        self.metrics['energy_consumption'] = max_energy - current_energy if max_energy > 0 else 0.0

        # 缓存命中率 (Paper Section 2.2)
        if self.cache_request_count > 0:
            self.metrics['cache_hit_rate'] = self.cache_hit_count / self.cache_request_count
        else:
            self.metrics['cache_hit_rate'] = 0.0

        # 队列利用率 (简化版本)
        total_queue_size = 0
        for rsu in self.rsus:
            for queue in rsu.queues.values():
                total_queue_size += len(queue.task_list)
        for uav in self.uavs:
            for queue in uav.queues.values():
                total_queue_size += len(queue.task_list)
        max_queue_capacity = len(self.rsus) * 10 + len(self.uavs) * 5  # 假设的最大容量
        self.metrics['queue_utilization'] = total_queue_size / max_queue_capacity if max_queue_capacity > 0 else 0.0

        # 综合性能分数
        completion_score = self.metrics['task_completion_rate']
        delay_score = max(0, 1 - self.metrics['total_delay'] / 100)  # 假设100为最大可接受延迟
        energy_score = max(0, 1 - self.metrics['energy_consumption'] / max_energy) if max_energy > 0 else 1.0
        self.metrics['combined_performance_score'] = (completion_score + delay_score + energy_score) / 3

    def _calculate_completion_probability(self, task) -> float:
        """计算任务完成概率 - 优化版本提升完成率"""
        base_prob = 0.75  # 大幅提高基础完成概率从50%到75%

        # 根据任务优先级调整 - 增强优先级效果
        if task.priority == 0:  # 高优先级
            base_prob *= 1.6  # 从1.5提升到1.6
        elif task.priority == 1:  # 中优先级
            base_prob *= 1.3  # 从1.2提升到1.3
        elif task.priority == 2:  # 低优先级
            base_prob *= 1.1  # 新增低优先级奖励

        # 根据分配节点类型调整 - 增强节点能力
        if task.assigned_node:
            # RSU处理能力更强
            if any(rsu.node_id == task.assigned_node for rsu in self.rsus):
                base_prob *= 1.5  # 从1.3提升到1.5
            # 固定位置UAV处理能力提升
            elif any(uav.node_id == task.assigned_node for uav in self.uavs):
                base_prob *= 1.4  # 从1.25提升到1.4
            # 本地处理能力改善
            else:
                base_prob *= 0.9  # 从0.8提升到0.9

        # 根据任务紧急程度调整 - 更宽容的时间策略
        time_remaining = task.max_tolerance - (self.current_time - task.arrival_time)
        if time_remaining <= 1:  # 极度紧急
            base_prob *= 1.2  # 紧急任务优先保证
        elif time_remaining <= 3:  # 紧急
            base_prob *= 1.3
        elif time_remaining <= 8:  # 正常
            base_prob *= 1.1
        elif time_remaining >= 15:  # 充裕
            base_prob *= 1.2

        # 根据任务类型调整 - 新增任务类型优化
        from models.data_structures import TaskType
        if hasattr(task, 'task_type'):
            if task.task_type == TaskType.EXTREMELY_SENSITIVE:
                base_prob *= 1.3  # 极敏感任务优先保证完成
            elif task.task_type == TaskType.DELAY_SENSITIVE:
                base_prob *= 1.2  # 敏感任务提升完成率
            elif task.task_type == TaskType.MODERATE_TOLERANT:
                base_prob *= 1.1  # 中度容忍任务小幅提升

        # 根据任务大小调整 - 更平衡的处理
        if hasattr(task, 'data_size'):
            if task.data_size > 8e6:  # 大任务
                base_prob *= 0.9  # 从0.8提升到0.9
            elif task.data_size < 2e6:  # 小任务
                base_prob *= 1.2  # 从1.1提升到1.2

        # 确保概率在合理范围内
        return min(max(base_prob, 0.2), 0.95)  # 提高最小概率到0.2，最大到0.95

    def _get_candidate_nodes(self, task) -> List[str]:
        """基于任务类型确定候选节点集合 - 优化版本"""
        from models.data_structures import TaskType

        candidate_nodes = []

        if task.task_type == TaskType.EXTREMELY_SENSITIVE:  # 极度延迟敏感型
            # 优先本地，但也考虑高性能RSU
            candidate_nodes = [task.source_vehicle_id]
            # 添加最近的高性能RSU作为备选
            candidate_nodes.extend([rsu.node_id for rsu in self.rsus[:1]])  # 最近的1个RSU
        elif task.task_type == TaskType.DELAY_SENSITIVE:  # 延迟敏感型
            # 本地 + 优质RSU + 最佳UAV
            candidate_nodes = [task.source_vehicle_id]
            candidate_nodes.extend([rsu.node_id for rsu in self.rsus[:3]])  # 前3个RSU
            candidate_nodes.extend([uav.node_id for uav in self.uavs[:1]])  # 最佳UAV
        elif task.task_type == TaskType.MODERATE_TOLERANT:  # 中度延迟容忍型
            # 更多选择，平衡性能和负载
            candidate_nodes = [task.source_vehicle_id]
            candidate_nodes.extend([rsu.node_id for rsu in self.rsus])
            candidate_nodes.extend([uav.node_id for uav in self.uavs])
        else:  # 延迟容忍型
            # 所有节点，优先考虑负载均衡
            candidate_nodes = [task.source_vehicle_id]
            candidate_nodes.extend([rsu.node_id for rsu in self.rsus])
            candidate_nodes.extend([uav.node_id for uav in self.uavs])
            # 对于延迟容忍任务，可以重复添加高性能节点增加选择概率
            candidate_nodes.extend([rsu.node_id for rsu in self.rsus[:2]])  # 重复添加前2个RSU

        return candidate_nodes

    def _check_cache_hit(self, task) -> str:
        """检查RSU缓存命中 (Paper Section 2.2) - 修复版本"""
        # 简化缓存ID，提高命中概率
        task_result_id = f"{int(task.data_size/1e6)}MB_{task.priority}"  # 按数据大小(MB)和优先级缓存

        for rsu in self.rsus:
            if rsu.node_id in self.rsu_cache:
                if task_result_id in self.rsu_cache[rsu.node_id]:
                    # 检查缓存是否过期 (假设缓存有效期为100时隙)
                    cache_time = self.rsu_cache[rsu.node_id][task_result_id]
                    if self.current_time - cache_time <= 100:
                        return rsu.node_id
                    else:
                        # 清除过期缓存
                        del self.rsu_cache[rsu.node_id][task_result_id]
        return None

    def _select_target_node(self, task, candidate_nodes: List[str], compute_node_idx: int) -> str:
        """在候选节点集合内选择目标节点"""
        if len(candidate_nodes) == 0:
            return task.source_vehicle_id

        # 将动作索引映射到候选节点
        node_idx = compute_node_idx % len(candidate_nodes)
        return candidate_nodes[node_idx]

    def _should_cache_task(self, task, rsu_id: str, cache_decision: float) -> bool:
        """智能缓存决策 - 激活缓存机制"""
        from models.data_structures import TaskType

        # 大幅提高基础缓存概率
        base_cache_prob = max(cache_decision, 0.6)  # 最低60%缓存概率

        # 任务类型影响缓存决策 - 增强缓存激励
        if task.task_type == TaskType.EXTREMELY_SENSITIVE:
            # 极度延迟敏感任务，高缓存价值
            base_cache_prob += 0.4  # 从0.3提升到0.4
        elif task.task_type == TaskType.DELAY_SENSITIVE:
            # 延迟敏感任务，中等缓存价值
            base_cache_prob += 0.3  # 从0.2提升到0.3
        elif task.task_type == TaskType.MODERATE_TOLERANT:
            # 中度容忍任务，一般缓存价值
            base_cache_prob += 0.2  # 从0.1提升到0.2
        else:
            # 延迟容忍任务也给予缓存奖励
            base_cache_prob += 0.1

        # 任务大小影响 - 更积极的缓存策略
        if task.data_size < 2e6:  # 小于2MB
            base_cache_prob += 0.3  # 从0.2提升到0.3
        elif task.data_size < 5e6:  # 小于5MB
            base_cache_prob += 0.2  # 新增中等大小任务缓存
        elif task.data_size > 8e6:  # 大于8MB
            base_cache_prob -= 0.05  # 从-0.1减少到-0.05

        # 缓存容量检查 - 更宽松的容量管理
        if rsu_id in self.rsu_cache and len(self.rsu_cache[rsu_id]) > 45:  # 从40提升到45
            base_cache_prob -= 0.1  # 从-0.2减少到-0.1

        # 历史请求频率 - 增强频繁模式缓存
        task_pattern = f"{task.source_vehicle_id}_{int(task.data_size/1e6)}"
        if self._is_frequent_pattern(task_pattern):
            base_cache_prob += 0.4  # 从0.3提升到0.4

        # 优先级影响缓存决策
        if task.priority == 0:  # 高优先级
            base_cache_prob += 0.2
        elif task.priority == 1:  # 中优先级
            base_cache_prob += 0.1

        return np.random.random() < min(max(base_cache_prob, 0.3), 1.0)  # 最低30%缓存概率

    def _is_frequent_pattern(self, pattern: str) -> bool:
        """检查是否为频繁请求模式"""
        # 简化版本：检查最近是否有类似请求
        for rsu_cache in self.rsu_cache.values():
            for cached_id in rsu_cache.keys():
                if pattern in cached_id:
                    return True
        return False

    def _cache_task_result(self, task, rsu_id: str):
        """缓存任务结果到RSU (Paper Section 2.2) - 修复版本"""
        if rsu_id not in self.rsu_cache:
            self.rsu_cache[rsu_id] = {}

        # 使用与检查相同的ID格式
        task_result_id = f"{int(task.data_size/1e6)}MB_{task.priority}"
        self.rsu_cache[rsu_id][task_result_id] = self.current_time

        # 智能缓存容量管理 - LRU策略
        max_cache_size = 50
        if len(self.rsu_cache[rsu_id]) > max_cache_size:
            # 移除最久未使用的缓存项
            oldest_key = min(self.rsu_cache[rsu_id].keys(),
                           key=lambda k: self.rsu_cache[rsu_id][k])
            del self.rsu_cache[rsu_id][oldest_key]
