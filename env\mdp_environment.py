"""
MDP环境实现
车联网边缘计算强化学习环境
"""

import gym
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import torch

from config.config import *
from models.data_structures import *
from models.communication import CommunicationModel
from models.computation import ComputationModel
from models.energy import EnergyModel
from models.queue_system import QueueSystem


class VehicularEdgeComputingEnv(gym.Env):
    """车联网边缘计算MDP环境"""

    def __init__(self, config: SystemConfig):
        super().__init__()

        self.config = config
        self.task_config = TaskConfig()
        self.vehicle_config = VehicleConfig()
        self.rsu_config = RSUConfig()
        self.uav_config = UAVConfig()
        self.comm_config = CommunicationConfig()
        self.opt_config = OptimizationConfig()        # 创建完整配置对象
        full_config = Config()
          # 初始化模型组件
        self.comm_model = CommunicationModel(self.comm_config)
        self.comp_model = ComputationModel(full_config)
        self.energy_model = EnergyModel(full_config)
        self.queue_system = QueueSystem(self.config, self.task_config) # 确保传递正确的 task_config

        # 环境状态
        self.current_time = 0
        self.vehicles: List[Vehicle] = []
        self.rsus: List[RSU] = []
        self.uavs: List[UAV] = []
        self.tasks: List[Task] = []

        # 历史统计数据
        self.completed_tasks_history: List[Task] = []
        self.failed_tasks_history: List[Task] = []
        self.total_energy_consumed = 0.0

        # 定义动作和状态空间
        self._setup_spaces()

        # 性能指标
        self.metrics = {
            'total_delay': 0.0,
            'energy_consumption': 0.0,
            'task_completion_rate': 0.0,
            'cache_hit_rate': 0.0,
            'queue_utilization': 0.0
        }

    def _setup_spaces(self):
        """设置动作和状态空间"""
        # 状态空间维度计算
        # 车辆状态: position(2) + compute_load(1) + energy(1) = 4 per vehicle
        # RSU状态: cache_utilization(1) + queue_states(priorities*lifetimes) + load(1)
        # UAV状态: position(3) + energy(1) + load(1) = 5 per UAV
        # 任务状态: priority(1) + lifetime(1) + size(1) + deadline(1) = 4 per task

        vehicle_state_dim = 4 * self.config.num_vehicles
        rsu_state_dim = (2 + self.task_config.num_priorities * self.task_config.max_lifetime) * self.config.num_rsus
        uav_state_dim = 5 * self.config.num_uavs
        task_state_dim = 4 * 50  # 最大50个活跃任务

        total_state_dim = vehicle_state_dim + rsu_state_dim + uav_state_dim + task_state_dim

        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(total_state_dim,),
            dtype=np.float32
        )

        # 动作空间: 任务分配决策
        # 每个任务的动作: [计算节点选择, 缓存决策, 迁移决策]
        # 计算节点: 0-本地, 1到num_rsus为RSU, num_rsus+1到num_rsus+num_uavs为UAV
        max_compute_nodes = 1 + self.config.num_rsus + self.config.num_uavs

        self.action_space = gym.spaces.MultiDiscrete([
            max_compute_nodes,  # 计算节点选择
            2,  # 缓存决策 (0: 不缓存, 1: 缓存)
            2   # 迁移决策 (0: 不迁移, 1: 迁移)
        ])

    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_time = 0
        self.tasks.clear()

        # 重置历史数据
        self.completed_tasks_history.clear()
        self.failed_tasks_history.clear()
        self.total_energy_consumed = 0.0

        # 初始化车辆
        self.vehicles = self._initialize_vehicles()

        # 初始化RSU
        self.rsus = self._initialize_rsus()

        # 初始化UAV
        self.uavs = self._initialize_uavs()

        # 初始化模型
        self.queue_system = QueueSystem(self.config, self.task_config)

        # 重置队列系统
        for rsu in self.rsus:
            # RSU队列初始化将在创建RSU时处理
            pass
        for uav in self.uavs:
            # UAV队列初始化将在创建UAV时处理
            pass

        # 重置性能指标
        self.metrics = {key: 0.0 for key in self.metrics}

        return self._get_state()

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一步仿真"""
        # 生成新任务
        new_tasks = self._generate_tasks()
        self.tasks.extend(new_tasks)

        # 执行动作 - 任务分配和调度
        self._execute_actions(action)

        # 更新车辆位置
        self._update_vehicle_positions()

        # 更新UAV位置
        self._update_uav_positions()

        # 处理任务执行
        self._process_task_execution()

        # 更新队列状态
        self._update_queue_states()

        # 计算奖励
        reward = self._calculate_reward()

        # 更新时间
        self.current_time += 1

        # 检查是否结束
        done = self.current_time >= self.config.max_time_slots

        # 更新性能指标
        self._update_metrics()

        info = {
            'metrics': self.metrics.copy(),
            'num_active_tasks': len(self.tasks),
            'current_time': self.current_time
        }

        return self._get_state(), reward, done, info

    def _initialize_vehicles(self) -> List[Vehicle]:
        """初始化车辆"""
        vehicles = []
        for i in range(self.config.num_vehicles):
            # 随机位置
            position = Position(
                x=np.random.uniform(0, self.config.area_width),
                y=np.random.uniform(0, self.config.area_height),
                z=0.0
            )

            # 随机计算能力
            compute_capacity = np.random.uniform(
                *self.vehicle_config.compute_capacity_range
            )

            vehicle = Vehicle(
                vehicle_id=f"V_{i}",                position=position,
                velocity=np.random.uniform(*self.vehicle_config.speed_range),
                direction=np.random.uniform(0, 2*np.pi),
                compute_capacity=compute_capacity,
                energy_level=self.vehicle_config.max_energy,
                max_energy=self.vehicle_config.max_energy,
                compute_load=0.0            )
            vehicles.append(vehicle)
        return vehicles

    def _initialize_rsus(self) -> List[RSU]:
        """初始化RSU"""
        rsus = []

        # RSU网格分布
        grid_size = int(np.sqrt(self.config.num_rsus))
        x_spacing = self.config.area_width / grid_size
        y_spacing = self.config.area_height / grid_size

        for i in range(self.config.num_rsus):
            row = i // grid_size
            col = i % grid_size

            position = Position(
                x=(col + 0.5) * x_spacing,
                y=(row + 0.5) * y_spacing,
                z=self.rsu_config.height
            )

            rsu = RSU(
                node_id=f"R_{i}",
                position=position,
                compute_capacity=self.rsu_config.compute_capacity,
                cache_capacity=self.rsu_config.cache_capacity,
                coverage_radius=self.rsu_config.coverage_radius,
                cache_storage={},
                compute_load=0.0
            )
            # RSU的队列会在__post_init__中自动初始化
            rsus.append(rsu)

        return rsus

    def _initialize_uavs(self) -> List[UAV]:
        """初始化UAV - 固定位置部署"""
        uavs = []

        # 预定义的战略性固定位置
        fixed_positions = [
            # 区域中心 - 最佳覆盖位置
            (self.config.area_width / 2, self.config.area_height / 2),
            # 区域四分之一点 - 平衡覆盖
            (self.config.area_width / 4, self.config.area_height / 4),
            # 其他战略位置
            (3 * self.config.area_width / 4, self.config.area_height / 4),
            (self.config.area_width / 4, 3 * self.config.area_height / 4),
            (3 * self.config.area_width / 4, 3 * self.config.area_height / 4),
        ]

        for i in range(self.config.num_uavs):
            # 使用预定义的固定位置，如果UAV数量超过预定义位置，则循环使用
            pos_idx = i % len(fixed_positions)
            x, y = fixed_positions[pos_idx]

            position = Position(
                x=x,
                y=y,
                z=self.uav_config.flying_height
            )

            uav = UAV(
                node_id=f"U_{i}",
                position=position,
                velocity=0.0,  # 固定位置，速度设为0
                compute_capacity=self.uav_config.compute_capacity,
                energy_level=self.uav_config.max_energy,
                max_energy=self.uav_config.max_energy,
                target_position=position,  # 目标位置与当前位置相同
                compute_load=0.0
            )
            # UAV的队列会在__post_init__中自动初始化
            uavs.append(uav)

        return uavs

    def _generate_tasks(self) -> List[Task]:
        """生成新任务"""
        tasks = []

        # 为每个车辆生成任务
        for vehicle in self.vehicles:
            if np.random.random() < self.task_config.task_arrival_rate:
                # 任务属性
                data_size = np.random.uniform(*self.task_config.data_size_range)
                computation_requirement = data_size * self.task_config.computation_density
                output_size = data_size * self.task_config.output_ratio
                max_tolerance = np.random.randint(*self.task_config.max_tolerance_range)
                priority = np.random.choice(
                    self.task_config.num_priorities,
                    p=self.task_config.priority_weights
                )

                task = Task(
                    task_id=f"T_{self.current_time}_{vehicle.vehicle_id}_{len(tasks)}",
                    source_vehicle_id=vehicle.vehicle_id,
                    data_size=data_size,
                    computation_requirement=computation_requirement,
                    output_size=output_size,
                    arrival_time=self.current_time,
                    max_tolerance=max_tolerance,
                    priority=priority,
                    lifetime=self.task_config.max_lifetime,
                    status=TaskStatus.WAITING
                )
                tasks.append(task)

        return tasks

    def _get_state(self) -> np.ndarray:
        """获取当前状态向量"""
        state_vector = []

        # 车辆状态
        for vehicle in self.vehicles:
            state_vector.extend([
                vehicle.position.x / self.config.area_width,
                vehicle.position.y / self.config.area_height,
                vehicle.compute_load,
                vehicle.energy_level / vehicle.max_energy
            ])
          # RSU状态
        for rsu in self.rsus:
            # 缓存利用率
            cache_utilization = len(rsu.cache_storage) / rsu.cache_capacity
            state_vector.append(cache_utilization)

            # 队列状态 - 使用RSU自己的队列
            for priority in range(self.task_config.num_priorities):
                for lifetime in range(self.task_config.max_lifetime):
                    key = (lifetime + 1, priority + 1)  # 调整索引
                    if key in rsu.queues:
                        count = len(rsu.queues[key].task_list)
                        state_vector.append(count / 10.0)  # 归一化
                    else:
                        state_vector.append(0.0)

            # 计算负载
            state_vector.append(rsu.compute_load)

        # UAV状态
        for uav in self.uavs:
            state_vector.extend([
                uav.position.x / self.config.area_width,
                uav.position.y / self.config.area_height,
                uav.position.z / 1000.0,  # 归一化高度
                uav.energy_level / uav.max_energy,
                uav.compute_load
            ])

        # 活跃任务状态 (最多50个)
        active_tasks = [t for t in self.tasks if t.status == TaskStatus.WAITING][:50]
        for i in range(50):
            if i < len(active_tasks):
                task = active_tasks[i]
                state_vector.extend([
                    task.priority / self.task_config.num_priorities,
                    task.lifetime / self.task_config.max_lifetime,
                    task.data_size / 10e6,  # 归一化数据大小
                    task.max_tolerance / 20.0  # 归一化截止时间
                ])
            else:
                state_vector.extend([0.0, 0.0, 0.0, 0.0])

        return np.array(state_vector, dtype=np.float32)

    def _execute_actions(self, action: np.ndarray):
        """执行动作 - 任务分配"""
        active_tasks = [t for t in self.tasks if t.status == TaskStatus.WAITING]

        if len(active_tasks) == 0:
            return

        # 优先处理高优先级和紧急任务
        active_tasks.sort(key=lambda t: (t.priority,
                                       self.current_time - t.arrival_time),
                         reverse=True)

        # 选择最优先的任务进行分配
        task = active_tasks[0]

        compute_node_idx = action[0]
        cache_decision = action[1]
        migration_decision = action[2]

        # 确定目标计算节点
        if compute_node_idx == 0:
            # 本地计算
            target_node = task.source_vehicle_id
        elif compute_node_idx <= self.config.num_rsus:
            # RSU计算
            target_node = self.rsus[compute_node_idx - 1].node_id
        else:
            # UAV计算
            uav_idx = compute_node_idx - self.config.num_rsus - 1
            if uav_idx < len(self.uavs):
                target_node = self.uavs[uav_idx].node_id
            else:
                target_node = task.source_vehicle_id  # 回退到本地

        # 分配任务
        task.assigned_node = target_node
        task.status = TaskStatus.PROCESSING

        # 添加到队列
        if target_node != task.source_vehicle_id:
            # 找到目标节点并添加任务到其队列
            for rsu in self.rsus:
                if rsu.node_id == target_node:
                    key = (task.lifetime, task.priority)
                    if key in rsu.queues:
                        rsu.queues[key].task_list.append(task)
                    break
            for uav in self.uavs:
                if uav.node_id == target_node:
                    key = (task.lifetime, task.priority)
                    if key in uav.queues:
                        uav.queues[key].task_list.append(task)
                    break

    def _update_vehicle_positions(self):
        """更新车辆位置"""
        for vehicle in self.vehicles:
            # 简单的随机游走模型
            dx = vehicle.velocity * self.config.delta_t * np.cos(vehicle.direction)
            dy = vehicle.velocity * self.config.delta_t * np.sin(vehicle.direction)

            vehicle.position.x += dx
            vehicle.position.y += dy

            # 边界处理
            if vehicle.position.x < 0 or vehicle.position.x > self.config.area_width:
                vehicle.direction = np.pi - vehicle.direction
            if vehicle.position.y < 0 or vehicle.position.y > self.config.area_height:
                vehicle.direction = -vehicle.direction

            vehicle.position.x = np.clip(vehicle.position.x, 0, self.config.area_width)
            vehicle.position.y = np.clip(vehicle.position.y, 0, self.config.area_height)

    def _update_uav_positions(self):
        """更新UAV位置 - 固定位置模式"""
        # UAV保持固定位置，不进行移动
        # 这可以提供稳定的服务覆盖和可预测的性能
        pass

    def _process_task_execution(self):
        """处理任务执行"""
        completed_tasks = []

        for task in self.tasks:
            if task.status == TaskStatus.PROCESSING:
                # 根据任务优先级和分配节点调整完成概率
                completion_prob = self._calculate_completion_probability(task)

                if np.random.random() < completion_prob:
                    task.status = TaskStatus.COMPLETED
                    task.completion_time = self.current_time
                    completed_tasks.append(task)

                    # 模拟能耗消耗
                    self._consume_energy(task)

            elif task.status == TaskStatus.WAITING:
                # 检查任务是否超时
                if self.current_time - task.arrival_time > task.max_tolerance:
                    task.status = TaskStatus.FAILED
                    completed_tasks.append(task)

        # 保存已完成或失败的任务到历史记录，然后移除
        for task in completed_tasks:
            if task in self.tasks:
                if task.status == TaskStatus.COMPLETED:
                    self.completed_tasks_history.append(task)
                elif task.status == TaskStatus.FAILED:
                    self.failed_tasks_history.append(task)
                self.tasks.remove(task)

    def _consume_energy(self, task):
        """模拟任务处理的能耗消耗"""
        # 简单的能耗模型：根据任务大小消耗能量
        energy_cost = task.data_size * 1e-9  # 简化的能耗计算

        # 从分配的节点消耗能量
        if task.assigned_node:
            # 查找对应的车辆或UAV并消耗能量
            for vehicle in self.vehicles:
                if vehicle.vehicle_id == task.assigned_node:
                    vehicle.energy_level = max(0, vehicle.energy_level - energy_cost)
                    break

            for uav in self.uavs:
                if uav.node_id == task.assigned_node:
                    uav.energy_level = max(0, uav.energy_level - energy_cost)
                    break

    def _update_queue_states(self):
        """更新队列状态"""
        # 创建默认决策（不进行任何处理或迁移）
        default_decisions = {}
        default_incoming_data = {}

        for rsu in self.rsus:
            self.queue_system.update_rsu_queues(rsu, default_decisions, default_incoming_data)
        for uav in self.uavs:
            self.queue_system.update_uav_queues(uav, default_decisions, default_incoming_data)

    def _calculate_reward(self) -> float:
        """计算奖励函数"""
        # 多目标奖励函数
        delay_penalty = 0.0
        energy_penalty = 0.0
        completion_reward = 0.0
        failure_penalty = 0.0

        # 当前时隙的任务奖励/惩罚
        current_completed = 0
        current_failed = 0

        for task in self.tasks:
            if task.status == TaskStatus.COMPLETED and task.completion_time == self.current_time:
                delay = task.completion_time - task.arrival_time
                # 根据延迟给予不同的奖励
                if delay <= 3:  # 快速完成
                    completion_reward += self.opt_config.completion_reward * 1.5
                elif delay <= 8:  # 正常完成
                    completion_reward += self.opt_config.completion_reward
                else:  # 慢速完成
                    completion_reward += self.opt_config.completion_reward * 0.7

                # 延迟惩罚
                delay_penalty -= delay * self.opt_config.delay_weight
                current_completed += 1

            elif task.status == TaskStatus.FAILED:
                failure_penalty -= self.opt_config.failure_penalty
                current_failed += 1

        # 能耗惩罚
        total_energy = sum(v.energy_level for v in self.vehicles)
        total_energy += sum(u.energy_level for u in self.uavs)
        max_total_energy = (len(self.vehicles) * self.vehicle_config.max_energy +
                           len(self.uavs) * self.uav_config.max_energy)
        energy_consumed = max_total_energy - total_energy
        energy_penalty = -self.opt_config.energy_weight * energy_consumed

        # 系统效率奖励
        efficiency_bonus = 0.0
        if len(self.completed_tasks_history) > 0:
            completion_rate = len(self.completed_tasks_history) / max(1,
                len(self.completed_tasks_history) + len(self.failed_tasks_history))
            if completion_rate > 0.5:  # 完成率超过50%给予奖励
                efficiency_bonus = (completion_rate - 0.5) * 20.0

        total_reward = completion_reward + delay_penalty + energy_penalty + failure_penalty + efficiency_bonus

        return total_reward

    def _update_metrics(self):
        """更新性能指标"""
        # 使用历史数据计算指标
        if self.completed_tasks_history:
            # 平均延迟
            total_delay = sum(t.completion_time - t.arrival_time for t in self.completed_tasks_history)
            self.metrics['total_delay'] = total_delay / len(self.completed_tasks_history)
        else:
            self.metrics['total_delay'] = 0.0

        # 任务完成率
        total_processed = len(self.completed_tasks_history) + len(self.failed_tasks_history)
        if total_processed > 0:
            self.metrics['task_completion_rate'] = len(self.completed_tasks_history) / total_processed
        else:
            self.metrics['task_completion_rate'] = 0.0

        # 能耗统计
        current_energy = sum(v.energy_level for v in self.vehicles) + sum(u.energy_level for u in self.uavs)
        max_energy = (len(self.vehicles) * self.vehicle_config.max_energy +
                     len(self.uavs) * self.uav_config.max_energy)
        self.metrics['energy_consumption'] = max_energy - current_energy if max_energy > 0 else 0.0

        # 缓存命中率 (简化版本)
        self.metrics['cache_hit_rate'] = 0.0  # 需要实际的缓存统计

        # 队列利用率 (简化版本)
        total_queue_size = 0
        for rsu in self.rsus:
            for queue in rsu.queues.values():
                total_queue_size += len(queue.task_list)
        for uav in self.uavs:
            for queue in uav.queues.values():
                total_queue_size += len(queue.task_list)
        max_queue_capacity = len(self.rsus) * 10 + len(self.uavs) * 5  # 假设的最大容量
        self.metrics['queue_utilization'] = total_queue_size / max_queue_capacity if max_queue_capacity > 0 else 0.0

        # 综合性能分数
        completion_score = self.metrics['task_completion_rate']
        delay_score = max(0, 1 - self.metrics['total_delay'] / 100)  # 假设100为最大可接受延迟
        energy_score = max(0, 1 - self.metrics['energy_consumption'] / max_energy) if max_energy > 0 else 1.0
        self.metrics['combined_performance_score'] = (completion_score + delay_score + energy_score) / 3

    def _calculate_completion_probability(self, task) -> float:
        """计算任务完成概率"""
        base_prob = 0.5  # 基础完成概率提高到50%

        # 根据任务优先级调整
        if task.priority == 0:  # 高优先级
            base_prob *= 1.5
        elif task.priority == 1:  # 中优先级
            base_prob *= 1.2
        # 低优先级保持基础概率

        # 根据分配节点类型调整
        if task.assigned_node:
            # RSU处理能力更强
            if any(rsu.node_id == task.assigned_node for rsu in self.rsus):
                base_prob *= 1.3
            # 固定位置UAV处理能力提升 - 更稳定的服务
            elif any(uav.node_id == task.assigned_node for uav in self.uavs):
                base_prob *= 1.25  # 从1.1提升到1.25，因为固定位置更稳定
            # 本地处理能力较弱
            else:
                base_prob *= 0.8

        # 根据任务紧急程度调整
        time_remaining = task.max_tolerance - (self.current_time - task.arrival_time)
        if time_remaining <= 2:  # 非常紧急
            base_prob *= 1.4
        elif time_remaining <= 5:  # 紧急
            base_prob *= 1.2

        # 确保概率在合理范围内
        return min(max(base_prob, 0.1), 0.9)
