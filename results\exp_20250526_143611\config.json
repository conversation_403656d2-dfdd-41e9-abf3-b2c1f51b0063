{"system": {"delta_t": 0.1, "max_time_slots": 50, "num_vehicles": 10, "num_rsus": 4, "num_uavs": 2, "area_width": 2000.0, "area_height": 2000.0}, "task": {"task_arrival_rate": 0.3, "data_size_range": [500000.0, 6000000.0], "computation_density": 800, "output_ratio": 0.1, "max_tolerance_range": [5, 25], "num_priorities": 3, "priority_weights": [0.25, 0.5, 0.25], "max_lifetime": 25, "task_type_distribution": [0.15, 0.35, 0.35, 0.15]}, "ppo": {"hidden_dim": 128, "num_layers": 2, "learning_rate": 0.0003, "lr_decay_steps": 10000, "lr_decay_rate": 0.8, "batch_size": 64, "n_epochs": 800, "update_epochs": 800, "clip_range": 0.2, "clip_epsilon": 0.2, "gamma": 0.99, "gae_lambda": 0.95, "steps_per_update": 256, "entropy_coef": 0.05, "value_loss_coef": 0.5, "max_grad_norm": 0.5, "total_timesteps": 100000, "eval_freq": 10000, "save_freq": 20000}}