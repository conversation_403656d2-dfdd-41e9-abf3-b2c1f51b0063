{"system": {"delta_t": 0.1, "max_time_slots": 50, "num_vehicles": 10, "num_rsus": 4, "num_uavs": 2, "area_width": 2000.0, "area_height": 2000.0}, "task": {"task_arrival_rate": 0.3, "data_size_range": [500000.0, 5000000.0], "computation_density": 800, "output_ratio": 0.1, "max_tolerance_range": [8, 25], "num_priorities": 3, "priority_weights": [0.3, 0.5, 0.2], "max_lifetime": 20}, "ppo": {"hidden_dim": 128, "num_layers": 2, "learning_rate": 0.0003, "lr_decay_steps": 10000, "lr_decay_rate": 0.8, "batch_size": 32, "n_epochs": 5, "update_epochs": 5, "clip_range": 0.2, "clip_epsilon": 0.2, "gamma": 0.99, "gae_lambda": 0.95, "steps_per_update": 256, "entropy_coef": 0.01, "value_loss_coef": 0.5, "max_grad_norm": 0.5, "total_timesteps": 10000, "eval_freq": 2000, "save_freq": 5000}}