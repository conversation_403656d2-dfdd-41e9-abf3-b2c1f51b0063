{"eval_results": {"episode_rewards": [191.03495610396988, 203.6167443277297, 225.4674034485626, 202.0129741826342, 158.97025896956555, 249.3445846673388, 185.63314488708042, 215.24163842906427, 319.6648147372002, 237.48711646780438], "episode_lengths": [50, 50, 50, 50, 50, 50, 50, 50, 50, 50], "performance_metrics": [{"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.6666666666666666, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9977777777777778}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 0.6, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9979999999999999}, {"total_delay": 1.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9955555555555556}, {"total_delay": 1.1428571428571428, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961904761904762}, {"total_delay": 1.1428571428571428, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961904761904762}, {"total_delay": 1.875, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.99375}, {"total_delay": 1.7, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9943333333333334}, {"total_delay": 2.272727272727273, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9924242424242425}, {"total_delay": 2.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9930555555555555}, {"total_delay": 2.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9930555555555555}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9933333333333333}, {"total_delay": 1.8666666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.8823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9545620915032679}, {"total_delay": 2.5, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9390350877192982}, {"total_delay": 2.3529411764705883, "energy_consumption": 0.0, "task_completion_rate": 0.8095238095238095, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9286647992530345}, {"total_delay": 2.2777777777777777, "energy_consumption": 0.0, "task_completion_rate": 0.8181818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9318013468013469}, {"total_delay": 2.210526315789474, "energy_consumption": 0.0, "task_completion_rate": 0.7916666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9231871345029239}, {"total_delay": 2.210526315789474, "energy_consumption": 0.0, "task_completion_rate": 0.7307692307692307, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9028879892037787}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 0.6896551724137931, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8890517241379311}, {"total_delay": 2.227272727272727, "energy_consumption": 0.0, "task_completion_rate": 0.6470588235294118, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8749286987522282}, {"total_delay": 2.130434782608696, "energy_consumption": 0.0, "task_completion_rate": 0.575, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.851231884057971}, {"total_delay": 2.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.5853658536585366, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8548441734417344}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8451851851851853}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8451851851851853}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 0.5416666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8405555555555555}, {"total_delay": 2.607142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.5185185185185185, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.83081569664903}, {"total_delay": 3.1379310344827585, "energy_consumption": 0.0, "task_completion_rate": 0.5087719298245614, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8257975398265779}, {"total_delay": 3.6333333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.4838709677419355, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8158458781362007}, {"total_delay": 4.129032258064516, "energy_consumption": 0.0, "task_completion_rate": 0.484375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.814361559139785}, {"total_delay": 4.0, "energy_consumption": 0.0, "task_completion_rate": 0.48484848484848486, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.814949494949495}, {"total_delay": 3.909090909090909, "energy_consumption": 0.0, "task_completion_rate": 0.4852941176470588, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8154010695187166}, {"total_delay": 4.323529411764706, "energy_consumption": 0.0, "task_completion_rate": 0.4927536231884058, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8165061096902528}, {"total_delay": 4.714285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.4861111111111111, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.812989417989418}, {"total_delay": 4.583333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.4864864864864865, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8135510510510512}, {"total_delay": 4.486486486486487, "energy_consumption": 0.0, "task_completion_rate": 0.46835443037974683, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8078298551716273}, {"total_delay": 4.421052631578948, "energy_consumption": 0.0, "task_completion_rate": 0.4691358024691358, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8083084253844488}, {"total_delay": 4.421052631578948, "energy_consumption": 0.0, "task_completion_rate": 0.4470588235294118, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8009494324045408}, {"total_delay": 4.384615384615385, "energy_consumption": 0.0, "task_completion_rate": 0.4431818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7997785547785549}, {"total_delay": 4.7, "energy_consumption": 0.0, "task_completion_rate": 0.43956043956043955, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7975201465201466}, {"total_delay": 4.904761904761905, "energy_consumption": 0.0, "task_completion_rate": 0.45161290322580644, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8008550947260625}, {"total_delay": 4.790697674418604, "energy_consumption": 0.0, "task_completion_rate": 0.45263157894736844, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8015748674010608}, {"total_delay": 4.681818181818182, "energy_consumption": 0.0, "task_completion_rate": 0.4536082474226804, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8022633552014996}, {"total_delay": 4.681818181818182, "energy_consumption": 0.0, "task_completion_rate": 0.4489795918367347, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8007204700061843}, {"total_delay": 5.260869565217392, "energy_consumption": 0.0, "task_completion_rate": 0.44660194174757284, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7979977486984663}, {"total_delay": 5.260869565217392, "energy_consumption": 0.0, "task_completion_rate": 0.44660194174757284, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7979977486984663}, {"total_delay": 5.51063829787234, "energy_consumption": 0.0, "task_completion_rate": 0.44339622641509435, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7960966144787903}, {"total_delay": 5.770833333333333, "energy_consumption": 0.0, "task_completion_rate": 0.4485981308411215, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7969632658359295}, {"total_delay": 5.653061224489796, "energy_consumption": 0.0, "task_completion_rate": 0.44545454545454544, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7963079777365492}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.75, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9975}, {"total_delay": 0.8, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9973333333333333}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.1428571428571428, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961904761904762}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.5454545454545454, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9948484848484848}, {"total_delay": 2.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9930555555555555}, {"total_delay": 2.6153846153846154, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9674725274725274}, {"total_delay": 2.4, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9711666666666666}, {"total_delay": 2.4, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9711666666666666}, {"total_delay": 2.4, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9711666666666666}, {"total_delay": 2.2941176470588234, "energy_consumption": 0.0, "task_completion_rate": 0.8947368421052632, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.957265221878225}, {"total_delay": 2.2941176470588234, "energy_consumption": 0.0, "task_completion_rate": 0.7727272727272727, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9165953654188949}, {"total_delay": 2.263157894736842, "energy_consumption": 0.0, "task_completion_rate": 0.7307692307692307, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9027125506072874}, {"total_delay": 2.9047619047619047, "energy_consumption": 0.0, "task_completion_rate": 0.65625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8757341269841269}, {"total_delay": 2.772727272727273, "energy_consumption": 0.0, "task_completion_rate": 0.6470588235294118, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8731105169340463}, {"total_delay": 2.772727272727273, "energy_consumption": 0.0, "task_completion_rate": 0.6470588235294118, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8731105169340463}, {"total_delay": 3.2916666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.6486486486486487, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8719106606606607}, {"total_delay": 3.2916666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.6486486486486487, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8719106606606607}, {"total_delay": 3.16, "energy_consumption": 0.0, "task_completion_rate": 0.6097560975609756, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8593853658536585}, {"total_delay": 3.16, "energy_consumption": 0.0, "task_completion_rate": 0.5319148936170213, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8334382978723404}, {"total_delay": 3.740740740740741, "energy_consumption": 0.0, "task_completion_rate": 0.5510204081632653, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8378710002519526}, {"total_delay": 3.6785714285714284, "energy_consumption": 0.0, "task_completion_rate": 0.5283018867924528, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8305053908355795}, {"total_delay": 3.6206896551724137, "energy_consumption": 0.0, "task_completion_rate": 0.5087719298245614, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8241883444242791}, {"total_delay": 4.133333333333334, "energy_consumption": 0.0, "task_completion_rate": 0.5084745762711864, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8223804143126178}, {"total_delay": 4.096774193548387, "energy_consumption": 0.0, "task_completion_rate": 0.5166666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8252329749103943}, {"total_delay": 4.424242424242424, "energy_consumption": 0.0, "task_completion_rate": 0.5238095238095238, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8265223665223665}, {"total_delay": 4.882352941176471, "energy_consumption": 0.0, "task_completion_rate": 0.5151515151515151, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8221093285799168}, {"total_delay": 5.257142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.49295774647887325, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8134621059691481}, {"total_delay": 5.257142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.47297297297297297, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8068005148005147}, {"total_delay": 5.513513513513513, "energy_consumption": 0.0, "task_completion_rate": 0.46835443037974683, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8044064317482039}, {"total_delay": 5.868421052631579, "energy_consumption": 0.0, "task_completion_rate": 0.475, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8054385964912281}, {"total_delay": 6.205128205128205, "energy_consumption": 0.0, "task_completion_rate": 0.48148148148148145, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8064767331433998}, {"total_delay": 6.525, "energy_consumption": 0.0, "task_completion_rate": 0.4878048780487805, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8075182926829267}, {"total_delay": 6.525, "energy_consumption": 0.0, "task_completion_rate": 0.4819277108433735, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8055592369477912}, {"total_delay": 6.690476190476191, "energy_consumption": 0.0, "task_completion_rate": 0.4772727272727273, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8034559884559885}, {"total_delay": 6.976744186046512, "energy_consumption": 0.0, "task_completion_rate": 0.4725274725274725, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8009200102223358}, {"total_delay": 6.818181818181818, "energy_consumption": 0.0, "task_completion_rate": 0.4631578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7983253588516747}, {"total_delay": 7.111111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.45454545454545453, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7944781144781144}, {"total_delay": 6.956521739130435, "energy_consumption": 0.0, "task_completion_rate": 0.45544554455445546, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.795293442387717}, {"total_delay": 7.212765957446808, "energy_consumption": 0.0, "task_completion_rate": 0.44761904761904764, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7918304626815266}, {"total_delay": 7.0625, "energy_consumption": 0.0, "task_completion_rate": 0.4444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7912731481481482}, {"total_delay": 6.918367346938775, "energy_consumption": 0.0, "task_completion_rate": 0.44545454545454544, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7920902906617192}, {"total_delay": 7.16, "energy_consumption": 0.0, "task_completion_rate": 0.44642857142857145, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7916095238095239}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 0.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.6666666666666666}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.6666666666666666, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9977777777777778}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.2857142857142858, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9957142857142857}, {"total_delay": 1.2857142857142858, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9957142857142857}, {"total_delay": 1.5555555555555556, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9948148148148149}, {"total_delay": 2.1, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.993}, {"total_delay": 1.9090909090909092, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9936363636363637}, {"total_delay": 2.4166666666666665, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9919444444444444}, {"total_delay": 2.4166666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9663034188034189}, {"total_delay": 3.466666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9676111111111112}, {"total_delay": 3.25, "energy_consumption": 0.0, "task_completion_rate": 0.8888888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9521296296296295}, {"total_delay": 3.0588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.8947368421052632, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9547162022703818}, {"total_delay": 2.9444444444444446, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.956851851851852}, {"total_delay": 2.8421052631578947, "energy_consumption": 0.0, "task_completion_rate": 0.8636363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9450717703349283}, {"total_delay": 3.35, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9332777777777778}, {"total_delay": 3.8095238095238093, "energy_consumption": 0.0, "task_completion_rate": 0.8076923076923077, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9231990231990231}, {"total_delay": 3.6363636363636362, "energy_consumption": 0.0, "task_completion_rate": 0.7857142857142857, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9164502164502165}, {"total_delay": 3.4782608695652173, "energy_consumption": 0.0, "task_completion_rate": 0.7419354838709677, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9023842917251051}, {"total_delay": 3.4782608695652173, "energy_consumption": 0.0, "task_completion_rate": 0.696969696969697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8873956960913483}, {"total_delay": 3.88, "energy_consumption": 0.0, "task_completion_rate": 0.6410256410256411, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8674085470085471}, {"total_delay": 3.769230769230769, "energy_consumption": 0.0, "task_completion_rate": 0.6341463414634146, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8654846779237023}, {"total_delay": 3.769230769230769, "energy_consumption": 0.0, "task_completion_rate": 0.5909090909090909, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8510722610722611}, {"total_delay": 3.607142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.5957446808510638, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8532244174265452}, {"total_delay": 4.172413793103448, "energy_consumption": 0.0, "task_completion_rate": 0.5918367346938775, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.850037532254281}, {"total_delay": 4.666666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.5882352941176471, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8471895424836601}, {"total_delay": 5.129032258064516, "energy_consumption": 0.0, "task_completion_rate": 0.5740740740740741, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8409279171644762}, {"total_delay": 4.96875, "energy_consumption": 0.0, "task_completion_rate": 0.5423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8308951271186441}, {"total_delay": 4.96875, "energy_consumption": 0.0, "task_completion_rate": 0.5423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8308951271186441}, {"total_delay": 5.294117647058823, "energy_consumption": 0.0, "task_completion_rate": 0.5396825396825397, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8289137877373172}, {"total_delay": 5.6571428571428575, "energy_consumption": 0.0, "task_completion_rate": 0.546875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8301011904761905}, {"total_delay": 5.6571428571428575, "energy_consumption": 0.0, "task_completion_rate": 0.5303030303030303, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8245772005772006}, {"total_delay": 5.837837837837838, "energy_consumption": 0.0, "task_completion_rate": 0.5285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8233976833976834}, {"total_delay": 5.837837837837838, "energy_consumption": 0.0, "task_completion_rate": 0.5138888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8185035035035035}, {"total_delay": 5.564102564102564, "energy_consumption": 0.0, "task_completion_rate": 0.527027027027027, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8237953337953338}, {"total_delay": 5.45, "energy_consumption": 0.0, "task_completion_rate": 0.5128205128205128, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.819440170940171}, {"total_delay": 5.829268292682927, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8139024390243902}, {"total_delay": 6.142857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.49411764705882355, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8108963585434174}, {"total_delay": 6.0, "energy_consumption": 0.0, "task_completion_rate": 0.48863636363636365, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8095454545454546}, {"total_delay": 5.863636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.4631578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8015071770334928}, {"total_delay": 5.863636363636363, "energy_consumption": 0.0, "task_completion_rate": 0.4631578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8015071770334928}, {"total_delay": 6.086956521739131, "energy_consumption": 0.0, "task_completion_rate": 0.46938775510204084, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8028393966282165}, {"total_delay": 6.086956521739131, "energy_consumption": 0.0, "task_completion_rate": 0.46938775510204084, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8028393966282165}, {"total_delay": 5.895833333333333, "energy_consumption": 0.0, "task_completion_rate": 0.48, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8070138888888888}, {"total_delay": 5.775510204081633, "energy_consumption": 0.0, "task_completion_rate": 0.4803921568627451, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8075456849406429}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.1666666666666667, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9961111111111111}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.96}, {"total_delay": 2.8181818181818183, "energy_consumption": 0.0, "task_completion_rate": 0.9166666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9628282828282829}, {"total_delay": 2.8181818181818183, "energy_consumption": 0.0, "task_completion_rate": 0.9166666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9628282828282829}, {"total_delay": 3.076923076923077, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9659340659340659}, {"total_delay": 2.857142857142857, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9682539682539683}, {"total_delay": 3.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9680555555555556}, {"total_delay": 3.125, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9369517543859649}, {"total_delay": 3.125, "energy_consumption": 0.0, "task_completion_rate": 0.8, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9229166666666666}, {"total_delay": 2.9411764705882355, "energy_consumption": 0.0, "task_completion_rate": 0.7727272727272727, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9144385026737968}, {"total_delay": 3.5, "energy_consumption": 0.0, "task_completion_rate": 0.75, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9049999999999999}, {"total_delay": 3.35, "energy_consumption": 0.0, "task_completion_rate": 0.7407407407407407, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9024135802469136}, {"total_delay": 3.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.7, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8892063492063492}, {"total_delay": 3.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.6774193548387096, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8816794674859191}, {"total_delay": 3.772727272727273, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8763131313131313}, {"total_delay": 3.652173913043478, "energy_consumption": 0.0, "task_completion_rate": 0.6388888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8674557165861514}, {"total_delay": 4.68, "energy_consumption": 0.0, "task_completion_rate": 0.6410256410256411, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8647418803418804}, {"total_delay": 4.68, "energy_consumption": 0.0, "task_completion_rate": 0.625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8593999999999999}, {"total_delay": 5.0, "energy_consumption": 0.0, "task_completion_rate": 0.6428571428571429, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8642857142857143}, {"total_delay": 5.464285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.6363636363636364, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.860573593073593}, {"total_delay": 5.275862068965517, "energy_consumption": 0.0, "task_completion_rate": 0.6304347826086957, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8592253873063468}, {"total_delay": 5.1, "energy_consumption": 0.0, "task_completion_rate": 0.5882352941176471, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8457450980392157}, {"total_delay": 4.967741935483871, "energy_consumption": 0.0, "task_completion_rate": 0.5849056603773585, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8450760803408399}, {"total_delay": 4.8125, "energy_consumption": 0.0, "task_completion_rate": 0.5423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8314159604519774}, {"total_delay": 4.8125, "energy_consumption": 0.0, "task_completion_rate": 0.5333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8284027777777778}, {"total_delay": 4.5588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.5396825396825397, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.831364768129474}, {"total_delay": 4.5588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.5151515151515151, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8231877599524658}, {"total_delay": 5.085714285714285, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8163809523809524}, {"total_delay": 5.405405405405405, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8153153153153153}, {"total_delay": 5.2631578947368425, "energy_consumption": 0.0, "task_completion_rate": 0.48717948717948717, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8115159694107063}, {"total_delay": 5.666666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.4936708860759494, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8123347398030942}, {"total_delay": 6.025, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.81325}, {"total_delay": 5.878048780487805, "energy_consumption": 0.0, "task_completion_rate": 0.47126436781609193, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8041612933370713}, {"total_delay": 6.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.47191011235955055, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8031763866595328}, {"total_delay": 6.093023255813954, "energy_consumption": 0.0, "task_completion_rate": 0.4725274725274725, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8038657466564443}, {"total_delay": 6.454545454545454, "energy_consumption": 0.0, "task_completion_rate": 0.4536082474226804, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7963542642924087}, {"total_delay": 6.777777777777778, "energy_consumption": 0.0, "task_completion_rate": 0.45918367346938777, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.79713529856387}, {"total_delay": 6.630434782608695, "energy_consumption": 0.0, "task_completion_rate": 0.45544554455445546, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7963803989094561}, {"total_delay": 6.48936170212766, "energy_consumption": 0.0, "task_completion_rate": 0.4392523364485981, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7914529064757737}, {"total_delay": 6.791666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.43636363636363634, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7894823232323231}, {"total_delay": 6.653061224489796, "energy_consumption": 0.0, "task_completion_rate": 0.4298245614035088, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.787764649719537}, {"total_delay": 6.54, "energy_consumption": 0.0, "task_completion_rate": 0.423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.786109604519774}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 0.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.6666666666666666}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.25, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9958333333333332}, {"total_delay": 1.8, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9940000000000001}, {"total_delay": 1.8, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9940000000000001}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.4285714285714286, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9952380952380953}, {"total_delay": 1.5555555555555556, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9948148148148149}, {"total_delay": 1.5555555555555556, "energy_consumption": 0.0, "task_completion_rate": 0.8181818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9342087542087542}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9394444444444444}, {"total_delay": 2.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.8571428571428571, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9454365079365079}, {"total_delay": 2.769230769230769, "energy_consumption": 0.0, "task_completion_rate": 0.8125, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9282692307692307}, {"total_delay": 2.5714285714285716, "energy_consumption": 0.0, "task_completion_rate": 0.8235294117647058, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9326050420168067}, {"total_delay": 2.4, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9364444444444445}, {"total_delay": 2.3125, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9396600877192983}, {"total_delay": 3.0588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.8095238095238095, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.926311858076564}, {"total_delay": 2.888888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.782608695652174, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9179066022544283}, {"total_delay": 2.736842105263158, "energy_consumption": 0.0, "task_completion_rate": 0.76, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9108771929824561}, {"total_delay": 2.6, "energy_consumption": 0.0, "task_completion_rate": 0.7407407407407407, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9049135802469136}, {"total_delay": 3.380952380952381, "energy_consumption": 0.0, "task_completion_rate": 0.7241379310344828, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8967761357416529}, {"total_delay": 4.045454545454546, "energy_consumption": 0.0, "task_completion_rate": 0.5945945945945946, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8513800163800164}, {"total_delay": 3.869565217391304, "energy_consumption": 0.0, "task_completion_rate": 0.5609756097560976, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8407599858607281}, {"total_delay": 4.5, "energy_consumption": 0.0, "task_completion_rate": 0.5454545454545454, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8334848484848485}, {"total_delay": 4.32, "energy_consumption": 0.0, "task_completion_rate": 0.5319148936170213, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8295716312056737}, {"total_delay": 4.32, "energy_consumption": 0.0, "task_completion_rate": 0.5319148936170213, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8295716312056737}, {"total_delay": 4.111111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.5192307692307693, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8260398860398861}, {"total_delay": 4.035714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.5185185185185185, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8260537918871252}, {"total_delay": 3.9655172413793105, "energy_consumption": 0.0, "task_completion_rate": 0.5087719298245614, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8230389191369228}, {"total_delay": 3.9, "energy_consumption": 0.0, "task_completion_rate": 0.5, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8203333333333332}, {"total_delay": 3.774193548387097, "energy_consumption": 0.0, "task_completion_rate": 0.49206349206349204, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8181071855265403}, {"total_delay": 3.65625, "energy_consumption": 0.0, "task_completion_rate": 0.49230769230769234, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8185817307692308}, {"total_delay": 3.65625, "energy_consumption": 0.0, "task_completion_rate": 0.48484848484848486, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8160953282828283}, {"total_delay": 4.117647058823529, "energy_consumption": 0.0, "task_completion_rate": 0.4788732394366197, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8125655896161281}, {"total_delay": 4.514285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.4666666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8071746031746031}, {"total_delay": 4.916666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.47368421052631576, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.808172514619883}, {"total_delay": 5.297297297297297, "energy_consumption": 0.0, "task_completion_rate": 0.4805194805194805, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8091821691821691}, {"total_delay": 5.684210526315789, "energy_consumption": 0.0, "task_completion_rate": 0.4523809523809524, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.798512949039265}, {"total_delay": 6.0, "energy_consumption": 0.0, "task_completion_rate": 0.4588235294117647, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7996078431372547}, {"total_delay": 6.325, "energy_consumption": 0.0, "task_completion_rate": 0.45454545454545453, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7970984848484849}, {"total_delay": 6.325, "energy_consumption": 0.0, "task_completion_rate": 0.4444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7937314814814815}, {"total_delay": 6.195121951219512, "energy_consumption": 0.0, "task_completion_rate": 0.43617021276595747, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7914063310845875}, {"total_delay": 6.5476190476190474, "energy_consumption": 0.0, "task_completion_rate": 0.4421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7922096908939015}, {"total_delay": 6.318181818181818, "energy_consumption": 0.0, "task_completion_rate": 0.44, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7922727272727274}, {"total_delay": 6.2, "energy_consumption": 0.0, "task_completion_rate": 0.4368932038834951, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.791631067961165}, {"total_delay": 6.565217391304348, "energy_consumption": 0.0, "task_completion_rate": 0.4423076923076923, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7922185061315496}, {"total_delay": 6.425531914893617, "energy_consumption": 0.0, "task_completion_rate": 0.4392523364485981, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7916656724332206}, {"total_delay": 6.791666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.43243243243243246, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.788171921921922}, {"total_delay": 7.061224489795919, "energy_consumption": 0.0, "task_completion_rate": 0.4298245614035088, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7864041055018499}, {"total_delay": 6.92, "energy_consumption": 0.0, "task_completion_rate": 0.423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7848429378531073}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.6666666666666666, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9977777777777778}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.4, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9953333333333333}, {"total_delay": 1.8571428571428572, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9938095238095238}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9933333333333333}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9933333333333333}, {"total_delay": 2.1, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.993}, {"total_delay": 2.1, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.993}, {"total_delay": 1.8333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9938888888888888}, {"total_delay": 2.3076923076923075, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9923076923076923}, {"total_delay": 2.142857142857143, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9928571428571429}, {"total_delay": 2.6666666666666665, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9911111111111112}, {"total_delay": 2.6666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9702777777777779}, {"total_delay": 3.0588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9712854030501089}, {"total_delay": 3.0588235294117645, "energy_consumption": 0.0, "task_completion_rate": 0.8947368421052632, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9547162022703818}, {"total_delay": 2.789473684210526, "energy_consumption": 0.0, "task_completion_rate": 0.8636363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9452472089314194}, {"total_delay": 2.65, "energy_consumption": 0.0, "task_completion_rate": 0.8, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9245000000000001}, {"total_delay": 3.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.7777777777777778, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9151322751322751}, {"total_delay": 3.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.7241379310344828, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8972523262178435}, {"total_delay": 3.238095238095238, "energy_consumption": 0.0, "task_completion_rate": 0.7241379310344828, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8972523262178435}, {"total_delay": 3.6956521739130435, "energy_consumption": 0.0, "task_completion_rate": 0.7419354838709677, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9016596540439458}, {"total_delay": 3.5416666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.7272727272727273, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8972853535353535}, {"total_delay": 3.4, "energy_consumption": 0.0, "task_completion_rate": 0.6756756756756757, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8805585585585586}, {"total_delay": 3.4615384615384617, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8773504273504273}, {"total_delay": 3.4615384615384617, "energy_consumption": 0.0, "task_completion_rate": 0.6341463414634146, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8665103189493433}, {"total_delay": 3.392857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.6511627906976745, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8724114064230344}, {"total_delay": 3.310344827586207, "energy_consumption": 0.0, "task_completion_rate": 0.6304347826086957, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8657771114442778}, {"total_delay": 3.935483870967742, "energy_consumption": 0.0, "task_completion_rate": 0.6458333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.868826164874552}, {"total_delay": 4.40625, "energy_consumption": 0.0, "task_completion_rate": 0.6530612244897959, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8696662414965987}, {"total_delay": 4.40625, "energy_consumption": 0.0, "task_completion_rate": 0.6274509803921569, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8611294934640523}, {"total_delay": 4.40625, "energy_consumption": 0.0, "task_completion_rate": 0.5925925925925926, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8495100308641975}, {"total_delay": 4.714285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.603448275862069, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8521018062397373}, {"total_delay": 4.714285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.5833333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8453968253968255}, {"total_delay": 5.0, "energy_consumption": 0.0, "task_completion_rate": 0.5522388059701493, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8340796019900498}, {"total_delay": 5.342105263157895, "energy_consumption": 0.0, "task_completion_rate": 0.5428571428571428, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.829812030075188}, {"total_delay": 5.342105263157895, "energy_consumption": 0.0, "task_completion_rate": 0.5428571428571428, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.829812030075188}, {"total_delay": 5.641025641025641, "energy_consumption": 0.0, "task_completion_rate": 0.5492957746478874, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8309618394125436}, {"total_delay": 5.5, "energy_consumption": 0.0, "task_completion_rate": 0.5263157894736842, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8237719298245615}, {"total_delay": 5.5, "energy_consumption": 0.0, "task_completion_rate": 0.5194805194805194, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8214935064935065}, {"total_delay": 5.380952380952381, "energy_consumption": 0.0, "task_completion_rate": 0.5185185185185185, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8215696649029982}, {"total_delay": 5.159090909090909, "energy_consumption": 0.0, "task_completion_rate": 0.5238095238095238, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8240728715728715}, {"total_delay": 5.044444444444444, "energy_consumption": 0.0, "task_completion_rate": 0.5172413793103449, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8222656449553002}, {"total_delay": 5.391304347826087, "energy_consumption": 0.0, "task_completion_rate": 0.5168539325842697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8209802963686696}, {"total_delay": 5.276595744680851, "energy_consumption": 0.0, "task_completion_rate": 0.5164835164835165, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8212391863455694}, {"total_delay": 5.166666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.5106382978723404, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8196572104018912}, {"total_delay": 5.510204081632653, "energy_consumption": 0.0, "task_completion_rate": 0.5157894736842106, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8202291442892946}, {"total_delay": 5.76, "energy_consumption": 0.0, "task_completion_rate": 0.5154639175257731, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8192879725085911}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 0.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.6666666666666666}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 0.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.6666666666666666}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9933333333333333}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9933333333333333}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9925}, {"total_delay": 2.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9922222222222222}, {"total_delay": 2.2857142857142856, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9923809523809523}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9925}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9925}, {"total_delay": 2.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9922222222222222}, {"total_delay": 2.3636363636363638, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9921212121212122}, {"total_delay": 2.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9922222222222222}, {"total_delay": 2.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9665811965811967}, {"total_delay": 2.5714285714285716, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9692063492063493}, {"total_delay": 2.6666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9702777777777779}, {"total_delay": 2.6666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.8823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9518954248366013}, {"total_delay": 2.6875, "energy_consumption": 0.0, "task_completion_rate": 0.7619047619047619, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9116765873015873}, {"total_delay": 2.764705882352941, "energy_consumption": 0.0, "task_completion_rate": 0.7727272727272727, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9150267379679144}, {"total_delay": 3.611111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.782608695652174, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9154991948470209}, {"total_delay": 3.611111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.782608695652174, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9154991948470209}, {"total_delay": 4.3, "energy_consumption": 0.0, "task_completion_rate": 0.7692307692307693, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9087435897435897}, {"total_delay": 4.142857142857143, "energy_consumption": 0.0, "task_completion_rate": 0.75, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9028571428571429}, {"total_delay": 3.869565217391304, "energy_consumption": 0.0, "task_completion_rate": 0.71875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8933514492753624}, {"total_delay": 3.7083333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8765277777777777}, {"total_delay": 4.4, "energy_consumption": 0.0, "task_completion_rate": 0.6578947368421053, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8712982456140351}, {"total_delay": 4.4, "energy_consumption": 0.0, "task_completion_rate": 0.5813953488372093, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8457984496124031}, {"total_delay": 4.4, "energy_consumption": 0.0, "task_completion_rate": 0.5813953488372093, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8457984496124031}, {"total_delay": 5.666666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8477777777777779}, {"total_delay": 5.666666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.574468085106383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8392671394799054}, {"total_delay": 6.172413793103448, "energy_consumption": 0.0, "task_completion_rate": 0.5471698113207547, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8284818911299068}, {"total_delay": 6.7, "energy_consumption": 0.0, "task_completion_rate": 0.5263157894736842, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8197719298245615}, {"total_delay": 6.375, "energy_consumption": 0.0, "task_completion_rate": 0.5161290322580645, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8174596774193548}, {"total_delay": 6.375, "energy_consumption": 0.0, "task_completion_rate": 0.5161290322580645, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8174596774193548}, {"total_delay": 6.181818181818182, "energy_consumption": 0.0, "task_completion_rate": 0.4852941176470588, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8078253119429591}, {"total_delay": 6.029411764705882, "energy_consumption": 0.0, "task_completion_rate": 0.4657534246575342, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8018197690034917}, {"total_delay": 5.914285714285715, "energy_consumption": 0.0, "task_completion_rate": 0.4605263157894737, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8004611528822055}, {"total_delay": 5.783783783783784, "energy_consumption": 0.0, "task_completion_rate": 0.46835443037974683, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.803505530847303}, {"total_delay": 5.7105263157894735, "energy_consumption": 0.0, "task_completion_rate": 0.4691358024691358, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8040101797704137}, {"total_delay": 5.7105263157894735, "energy_consumption": 0.0, "task_completion_rate": 0.4634146341463415, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8021031236628154}, {"total_delay": 5.641025641025641, "energy_consumption": 0.0, "task_completion_rate": 0.4642857142857143, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8026251526251525}, {"total_delay": 6.073170731707317, "energy_consumption": 0.0, "task_completion_rate": 0.47126436781609193, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8035108868330063}, {"total_delay": 6.380952380952381, "energy_consumption": 0.0, "task_completion_rate": 0.4666666666666667, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.800952380952381}, {"total_delay": 6.674418604651163, "energy_consumption": 0.0, "task_completion_rate": 0.4574468085106383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7969008741547089}, {"total_delay": 6.5227272727272725, "energy_consumption": 0.0, "task_completion_rate": 0.4583333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7977020202020202}, {"total_delay": 6.5227272727272725, "energy_consumption": 0.0, "task_completion_rate": 0.4444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7930723905723905}, {"total_delay": 6.304347826086956, "energy_consumption": 0.0, "task_completion_rate": 0.4423076923076923, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.793088071348941}, {"total_delay": 6.304347826086956, "energy_consumption": 0.0, "task_completion_rate": 0.42990654205607476, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7889543545984017}, {"total_delay": 6.638297872340425, "energy_consumption": 0.0, "task_completion_rate": 0.43119266055045874, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7882698939423515}, {"total_delay": 6.408163265306122, "energy_consumption": 0.0, "task_completion_rate": 0.4298245614035088, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.7885809762501492}, {"total_delay": 6.3, "energy_consumption": 0.0, "task_completion_rate": 0.423728813559322, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.786909604519774}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9966666666666667}, {"total_delay": 1.2, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.996}, {"total_delay": 1.4285714285714286, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9952380952380953}, {"total_delay": 1.4285714285714286, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9952380952380953}, {"total_delay": 2.111111111111111, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.992962962962963}, {"total_delay": 1.9, "energy_consumption": 0.0, "task_completion_rate": 0.9090909090909091, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9633636363636363}, {"total_delay": 1.7272727272727273, "energy_consumption": 0.0, "task_completion_rate": 0.9166666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9664646464646465}, {"total_delay": 2.1666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9671367521367521}, {"total_delay": 2.1666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9671367521367521}, {"total_delay": 2.5, "energy_consumption": 0.0, "task_completion_rate": 0.875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9500000000000001}, {"total_delay": 2.3333333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.8823529411764706, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9530065359477123}, {"total_delay": 2.8125, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9379934210526315}, {"total_delay": 2.8125, "energy_consumption": 0.0, "task_completion_rate": 0.8421052631578947, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9379934210526315}, {"total_delay": 3.111111111111111, "energy_consumption": 0.0, "task_completion_rate": 0.8571428571428571, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9420105820105821}, {"total_delay": 3.473684210526316, "energy_consumption": 0.0, "task_completion_rate": 0.8636363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9429665071770335}, {"total_delay": 3.473684210526316, "energy_consumption": 0.0, "task_completion_rate": 0.7916666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9189766081871346}, {"total_delay": 3.35, "energy_consumption": 0.0, "task_completion_rate": 0.7692307692307693, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9119102564102564}, {"total_delay": 3.2857142857142856, "energy_consumption": 0.0, "task_completion_rate": 0.7, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8890476190476191}, {"total_delay": 3.1363636363636362, "energy_consumption": 0.0, "task_completion_rate": 0.7096774193548387, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8927712609970674}, {"total_delay": 3.130434782608696, "energy_consumption": 0.0, "task_completion_rate": 0.6764705882352942, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8817220801364024}, {"total_delay": 3.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8786111111111111}, {"total_delay": 3.0833333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.856388888888889}, {"total_delay": 4.037037037037037, "energy_consumption": 0.0, "task_completion_rate": 0.6136363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8577553310886644}, {"total_delay": 4.464285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.6222222222222222, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8591931216931217}, {"total_delay": 4.827586206896552, "energy_consumption": 0.0, "task_completion_rate": 0.6041666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.851963601532567}, {"total_delay": 4.827586206896552, "energy_consumption": 0.0, "task_completion_rate": 0.6041666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.851963601532567}, {"total_delay": 5.2, "energy_consumption": 0.0, "task_completion_rate": 0.6, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8493333333333334}, {"total_delay": 4.9375, "energy_consumption": 0.0, "task_completion_rate": 0.6037735849056604, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8514661949685535}, {"total_delay": 4.787878787878788, "energy_consumption": 0.0, "task_completion_rate": 0.5892857142857143, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8471356421356422}, {"total_delay": 5.147058823529412, "energy_consumption": 0.0, "task_completion_rate": 0.576271186440678, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8416001994017946}, {"total_delay": 5.0, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8351851851851851}, {"total_delay": 4.888888888888889, "energy_consumption": 0.0, "task_completion_rate": 0.5454545454545454, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8321885521885521}, {"total_delay": 5.1891891891891895, "energy_consumption": 0.0, "task_completion_rate": 0.5285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8255598455598455}, {"total_delay": 5.5, "energy_consumption": 0.0, "task_completion_rate": 0.5135135135135135, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8195045045045045}, {"total_delay": 5.794871794871795, "energy_consumption": 0.0, "task_completion_rate": 0.5131578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8184030589293747}, {"total_delay": 5.794871794871795, "energy_consumption": 0.0, "task_completion_rate": 0.5131578947368421, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8184030589293747}, {"total_delay": 5.951219512195122, "energy_consumption": 0.0, "task_completion_rate": 0.5189873417721519, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8198250488834002}, {"total_delay": 5.809523809523809, "energy_consumption": 0.0, "task_completion_rate": 0.5121951219512195, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8180332946186605}, {"total_delay": 6.093023255813954, "energy_consumption": 0.0, "task_completion_rate": 0.4942528735632184, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.811107547001693}, {"total_delay": 5.954545454545454, "energy_consumption": 0.0, "task_completion_rate": 0.4943820224719101, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8116121893088185}, {"total_delay": 6.266666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.4891304347826087, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8088212560386473}, {"total_delay": 6.543478260869565, "energy_consumption": 0.0, "task_completion_rate": 0.4842105263157895, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8062585812356979}, {"total_delay": 6.404255319148936, "energy_consumption": 0.0, "task_completion_rate": 0.4845360824742268, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8068311764275791}, {"total_delay": 6.291666666666667, "energy_consumption": 0.0, "task_completion_rate": 0.48, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8056944444444444}, {"total_delay": 6.183673469387755, "energy_consumption": 0.0, "task_completion_rate": 0.47572815533980584, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8046304735486428}, {"total_delay": 6.08, "energy_consumption": 0.0, "task_completion_rate": 0.47619047619047616, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8051301587301588}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.8, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9973333333333333}, {"total_delay": 0.8, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9973333333333333}, {"total_delay": 0.7142857142857143, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9976190476190476}, {"total_delay": 0.625, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9979166666666667}, {"total_delay": 1.2222222222222223, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9959259259259259}, {"total_delay": 1.2222222222222223, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9959259259259259}, {"total_delay": 1.2222222222222223, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9625925925925927}, {"total_delay": 1.8333333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9682478632478633}, {"total_delay": 1.8333333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.9230769230769231, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9682478632478633}, {"total_delay": 1.7692307692307692, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9702930402930403}, {"total_delay": 1.7142857142857142, "energy_consumption": 0.0, "task_completion_rate": 0.875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9526190476190477}, {"total_delay": 2.25, "energy_consumption": 0.0, "task_completion_rate": 0.8888888888888888, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.955462962962963}, {"total_delay": 2.1176470588235294, "energy_consumption": 0.0, "task_completion_rate": 0.8947368421052632, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9578534571723427}, {"total_delay": 2.5555555555555554, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9581481481481481}, {"total_delay": 2.5555555555555554, "energy_consumption": 0.0, "task_completion_rate": 0.9, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9581481481481481}, {"total_delay": 3.4, "energy_consumption": 0.0, "task_completion_rate": 0.8695652173913043, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9451884057971015}, {"total_delay": 3.4, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9331111111111111}, {"total_delay": 3.4, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9331111111111111}, {"total_delay": 3.6363636363636362, "energy_consumption": 0.0, "task_completion_rate": 0.8461538461538461, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9365967365967366}, {"total_delay": 3.9565217391304346, "energy_consumption": 0.0, "task_completion_rate": 0.8214285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.927287784679089}, {"total_delay": 4.16, "energy_consumption": 0.0, "task_completion_rate": 0.8064516129032258, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9216172043010752}, {"total_delay": 4.346153846153846, "energy_consumption": 0.0, "task_completion_rate": 0.8125, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9230128205128206}, {"total_delay": 4.518518518518518, "energy_consumption": 0.0, "task_completion_rate": 0.8181818181818182, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9243322109988776}, {"total_delay": 4.714285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.8, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9176190476190476}, {"total_delay": 4.896551724137931, "energy_consumption": 0.0, "task_completion_rate": 0.8055555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9188633461047254}, {"total_delay": 4.733333333333333, "energy_consumption": 0.0, "task_completion_rate": 0.8108108108108109, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9211591591591591}, {"total_delay": 4.612903225806452, "energy_consumption": 0.0, "task_completion_rate": 0.7948717948717948, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9162475875379101}, {"total_delay": 4.5, "energy_consumption": 0.0, "task_completion_rate": 0.8, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9183333333333333}, {"total_delay": 4.424242424242424, "energy_consumption": 0.0, "task_completion_rate": 0.7857142857142857, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9138239538239539}, {"total_delay": 4.424242424242424, "energy_consumption": 0.0, "task_completion_rate": 0.7674418604651163, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.907733145407564}, {"total_delay": 4.352941176470588, "energy_consumption": 0.0, "task_completion_rate": 0.7727272727272727, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9097326203208556}, {"total_delay": 4.194444444444445, "energy_consumption": 0.0, "task_completion_rate": 0.75, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9026851851851853}, {"total_delay": 4.194444444444445, "energy_consumption": 0.0, "task_completion_rate": 0.7346938775510204, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8975831443688587}, {"total_delay": 4.026315789473684, "energy_consumption": 0.0, "task_completion_rate": 0.7307692307692307, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8968353576248314}, {"total_delay": 4.358974358974359, "energy_consumption": 0.0, "task_completion_rate": 0.7090909090909091, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8885003885003885}, {"total_delay": 4.358974358974359, "energy_consumption": 0.0, "task_completion_rate": 0.7090909090909091, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8885003885003885}, {"total_delay": 4.358974358974359, "energy_consumption": 0.0, "task_completion_rate": 0.6724137931034483, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.876274683171235}, {"total_delay": 4.595238095238095, "energy_consumption": 0.0, "task_completion_rate": 0.65625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8700992063492063}, {"total_delay": 4.930232558139535, "energy_consumption": 0.0, "task_completion_rate": 0.6417910447761194, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8641629063982413}, {"total_delay": 5.25, "energy_consumption": 0.0, "task_completion_rate": 0.6197183098591549, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8557394366197183}, {"total_delay": 5.555555555555555, "energy_consumption": 0.0, "task_completion_rate": 0.625, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8564814814814815}, {"total_delay": 5.555555555555555, "energy_consumption": 0.0, "task_completion_rate": 0.6081081081081081, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8508508508508509}, {"total_delay": 5.456521739130435, "energy_consumption": 0.0, "task_completion_rate": 0.6052631578947368, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8502326468344775}, {"total_delay": 5.382978723404255, "energy_consumption": 0.0, "task_completion_rate": 0.5949367088607594, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8470356405422389}, {"total_delay": 5.63265306122449, "energy_consumption": 0.0, "task_completion_rate": 0.6049382716049383, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8495372469975644}, {"total_delay": 5.96, "energy_consumption": 0.0, "task_completion_rate": 0.6097560975609756, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8500520325203252}, {"total_delay": 0.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 1.0}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9988888888888888}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9983333333333334}, {"total_delay": 0.4, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9986666666666667}, {"total_delay": 1.7142857142857142, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9942857142857143}, {"total_delay": 1.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.995}, {"total_delay": 1.3333333333333333, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9955555555555556}, {"total_delay": 2.0, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9933333333333333}, {"total_delay": 1.8181818181818181, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.993939393939394}, {"total_delay": 2.5, "energy_consumption": 0.0, "task_completion_rate": 1.0, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9916666666666667}, {"total_delay": 2.3076923076923075, "energy_consumption": 0.0, "task_completion_rate": 0.9285714285714286, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9684981684981685}, {"total_delay": 2.9285714285714284, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9680158730158731}, {"total_delay": 2.9285714285714284, "energy_consumption": 0.0, "task_completion_rate": 0.9333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9680158730158731}, {"total_delay": 2.8, "energy_consumption": 0.0, "task_completion_rate": 0.9375, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9698333333333333}, {"total_delay": 2.5294117647058822, "energy_consumption": 0.0, "task_completion_rate": 0.9444444444444444, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9730501089324619}, {"total_delay": 3.1666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.9473684210526315, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9719005847953216}, {"total_delay": 3.1666666666666665, "energy_consumption": 0.0, "task_completion_rate": 0.8571428571428571, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9418253968253968}, {"total_delay": 3.65, "energy_consumption": 0.0, "task_completion_rate": 0.8695652173913043, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9443550724637682}, {"total_delay": 3.65, "energy_consumption": 0.0, "task_completion_rate": 0.8333333333333334, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9322777777777778}, {"total_delay": 3.4761904761904763, "energy_consumption": 0.0, "task_completion_rate": 0.8076923076923077, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9243101343101343}, {"total_delay": 3.3181818181818183, "energy_consumption": 0.0, "task_completion_rate": 0.7333333333333333, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.9000505050505051}, {"total_delay": 3.9565217391304346, "energy_consumption": 0.0, "task_completion_rate": 0.71875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8930615942028984}, {"total_delay": 3.9583333333333335, "energy_consumption": 0.0, "task_completion_rate": 0.6666666666666666, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8756944444444444}, {"total_delay": 3.8, "energy_consumption": 0.0, "task_completion_rate": 0.6410256410256411, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8676752136752137}, {"total_delay": 3.5925925925925926, "energy_consumption": 0.0, "task_completion_rate": 0.6428571428571429, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8689770723104057}, {"total_delay": 4.214285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.6222222222222222, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.860026455026455}, {"total_delay": 4.214285714285714, "energy_consumption": 0.0, "task_completion_rate": 0.5957446808510638, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8512006079027356}, {"total_delay": 4.068965517241379, "energy_consumption": 0.0, "task_completion_rate": 0.5686274509803921, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8426459319359928}, {"total_delay": 4.419354838709677, "energy_consumption": 0.0, "task_completion_rate": 0.5740740740740741, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8432935085623258}, {"total_delay": 4.419354838709677, "energy_consumption": 0.0, "task_completion_rate": 0.5636363636363636, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8398142717497556}, {"total_delay": 4.9375, "energy_consumption": 0.0, "task_completion_rate": 0.5714285714285714, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8406845238095239}, {"total_delay": 5.617647058823529, "energy_consumption": 0.0, "task_completion_rate": 0.576271186440678, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8400315719508141}, {"total_delay": 5.885714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.5555555555555556, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8322328042328042}, {"total_delay": 5.885714285714286, "energy_consumption": 0.0, "task_completion_rate": 0.546875, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8293392857142857}, {"total_delay": 5.75, "energy_consumption": 0.0, "task_completion_rate": 0.5454545454545454, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8293181818181817}, {"total_delay": 5.621621621621622, "energy_consumption": 0.0, "task_completion_rate": 0.5441176470588235, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8293004769475357}, {"total_delay": 5.358974358974359, "energy_consumption": 0.0, "task_completion_rate": 0.5492957746478874, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8319020103527146}, {"total_delay": 5.25, "energy_consumption": 0.0, "task_completion_rate": 0.547945205479452, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8318150684931508}, {"total_delay": 5.609756097560975, "energy_consumption": 0.0, "task_completion_rate": 0.5189873417721519, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.820963260265514}, {"total_delay": 5.9523809523809526, "energy_consumption": 0.0, "task_completion_rate": 0.5185185185185185, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8196649029982362}, {"total_delay": 6.209302325581396, "energy_consumption": 0.0, "task_completion_rate": 0.5180722891566265, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8186597553002709}, {"total_delay": 6.4772727272727275, "energy_consumption": 0.0, "task_completion_rate": 0.5176470588235295, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8176247771836008}, {"total_delay": 6.7555555555555555, "energy_consumption": 0.0, "task_completion_rate": 0.5172413793103449, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8165619412515964}, {"total_delay": 6.608695652173913, "energy_consumption": 0.0, "task_completion_rate": 0.5168539325842697, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8169223253541769}, {"total_delay": 6.8936170212765955, "energy_consumption": 0.0, "task_completion_rate": 0.5108695652173914, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.813977798334875}, {"total_delay": 7.125, "energy_consumption": 0.0, "task_completion_rate": 0.48484848484848486, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8045328282828282}, {"total_delay": 7.346938775510204, "energy_consumption": 0.0, "task_completion_rate": 0.47572815533980584, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.8007529225282347}, {"total_delay": 7.2, "energy_consumption": 0.0, "task_completion_rate": 0.4807692307692308, "cache_hit_rate": 0.0, "queue_utilization": 0.0, "combined_performance_score": 0.802923076923077}], "task_completion_rates": [0.44545454545454544, 0.44642857142857145, 0.4803921568627451, 0.423728813559322, 0.423728813559322, 0.5154639175257731, 0.423728813559322, 0.47619047619047616, 0.6097560975609756, 0.4807692307692308], "average_delays": [5.653061224489796, 7.16, 5.775510204081633, 6.54, 6.92, 5.76, 6.3, 6.08, 5.96, 7.2]}, "eval_stats": {"mean_reward": 218.847363622095, "std_reward": 41.80413369517521, "mean_length": 50.0, "mean_completion_rate": 0.47256414364702837, "mean_delay": 6.334857142857143}}