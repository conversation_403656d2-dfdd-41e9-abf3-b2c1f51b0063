"""
简化版MAPPO算法实现
专门针对车联网边缘计算场景的多智能体PPO
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass
import gym

from algorithms.ppo import PPOAgent, PPOConfig


@dataclass
class SimpleMAPPOConfig(PPOConfig):
    """简化MAPPO配置"""
    num_agents: int = 3  # 智能体数量
    cooperation_weight: float = 0.1  # 协作权重


class SimpleMAPPOAgent:
    """简化MAPPO智能体 - 基于PPO智能体的包装"""
    
    def __init__(self, agent_id: str, state_dim: int, action_space: gym.Space, config: SimpleMAPPOConfig):
        self.agent_id = agent_id
        self.config = config
        
        # 创建底层PPO智能体
        self.ppo_agent = PPOAgent(state_dim, action_space, config)
        
        # 多智能体特有的经验缓冲
        self.shared_experiences = []
    
    def get_action(self, state: np.ndarray, deterministic: bool = False):
        """获取动作 - 直接使用PPO智能体"""
        return self.ppo_agent.get_action(state, deterministic)
    
    def store_transition(self, state: np.ndarray, action: np.ndarray, 
                        logprob: float, reward: float, value: float, done: bool):
        """存储转换"""
        self.ppo_agent.store_transition(state, action, logprob, reward, value, done)
    
    def update(self, shared_info: Dict = None):
        """更新网络 - 增加协作学习"""
        # 基础PPO更新
        base_metrics = self.ppo_agent.update()
        
        # 如果有共享信息，进行协作学习
        if shared_info and len(self.ppo_agent.states) > 0:
            cooperation_loss = self._compute_cooperation_loss(shared_info)
            base_metrics['cooperation_loss'] = cooperation_loss
        
        return base_metrics
    
    def _compute_cooperation_loss(self, shared_info: Dict) -> float:
        """计算协作损失 - 简化版本"""
        # 这里可以实现智能体间的协作学习
        # 简化版本：返回0
        return 0.0
    
    def save(self, path: str):
        """保存模型"""
        self.ppo_agent.save(path)
    
    def load(self, path: str):
        """加载模型"""
        self.ppo_agent.load(path)


class SimpleMAPPOTrainer:
    """简化MAPPO训练器"""
    
    def __init__(self, env, agents: Dict[str, SimpleMAPPOAgent], config: SimpleMAPPOConfig):
        self.env = env
        self.agents = agents
        self.config = config
        
        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.training_metrics = []
    
    def train(self, total_timesteps: int) -> Dict:
        """训练MAPPO"""
        timestep = 0
        episode = 0
        
        print(f"开始简化MAPPO训练，总时间步数: {total_timesteps}")
        
        while timestep < total_timesteps:
            # 重置环境
            state = self.env.reset()
            episode_reward = {agent_id: 0 for agent_id in self.agents.keys()}
            episode_length = 0
            done = False
            
            while not done and timestep < total_timesteps:
                # 每个智能体独立决策（简化版本）
                # 在实际应用中，可以设计更复杂的协作策略
                
                # 使用第一个智能体（RSU）进行决策
                main_agent = list(self.agents.values())[0]
                action, logprob, value = main_agent.get_action(state)
                
                # 执行动作
                next_state, reward, done, info = self.env.step(action.squeeze())
                
                # 为所有智能体存储经验（简化版本：相同的经验）
                for agent_id, agent in self.agents.items():
                    agent.store_transition(state, action.squeeze(), logprob.item(), 
                                         reward, value.item(), done)
                    episode_reward[agent_id] += reward
                
                state = next_state
                episode_length += 1
                timestep += 1
                
                # 定期更新（每个episode结束或达到更新步数）
                if done or timestep % self.config.steps_per_update == 0:
                    # 收集共享信息
                    shared_info = self._collect_shared_info()
                    
                    # 更新所有智能体
                    update_metrics = {}
                    for agent_id, agent in self.agents.items():
                        if len(agent.ppo_agent.states) > 0:
                            metrics = agent.update(shared_info)
                            for k, v in metrics.items():
                                update_metrics[f'{agent_id}_{k}'] = v
                    
                    self.training_metrics.append(update_metrics)
            
            # Episode结束，记录统计信息
            avg_episode_reward = np.mean(list(episode_reward.values()))
            self.episode_rewards.append(avg_episode_reward)
            self.episode_lengths.append(episode_length)
            
            episode += 1
            
            # 打印训练进度
            if episode % 10 == 0:
                avg_reward = np.mean(self.episode_rewards[-10:])
                avg_length = np.mean(self.episode_lengths[-10:])
                print(f"Simple MAPPO - Timestep: {timestep}, Episodes: {episode}, "
                      f"Avg Reward: {avg_reward:.2f}, Avg Length: {avg_length:.2f}")
        
        return {
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'training_metrics': self.training_metrics
        }
    
    def _collect_shared_info(self) -> Dict:
        """收集智能体间的共享信息"""
        shared_info = {
            'num_agents': len(self.agents),
            'agent_ids': list(self.agents.keys())
        }
        
        # 可以添加更多共享信息，如：
        # - 平均奖励
        # - 策略参数
        # - 经验统计等
        
        return shared_info


def evaluate_simple_mappo_agent(env, agents: Dict[str, SimpleMAPPOAgent], num_episodes: int = 10):
    """评估简化MAPPO智能体性能"""
    eval_results = {
        'episode_rewards': [],
        'episode_lengths': [],
        'performance_metrics': [],
        'task_completion_rates': [],
        'average_delays': []
    }

    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False

        while not done:
            # 使用第一个智能体进行评估
            main_agent = list(agents.values())[0]
            action, _, _ = main_agent.get_action(state, deterministic=True)
            state, reward, done, info = env.step(action.squeeze())

            episode_reward += reward
            episode_length += 1

            if 'metrics' in info:
                eval_results['performance_metrics'].append(info['metrics'])

        eval_results['episode_rewards'].append(episode_reward)
        eval_results['episode_lengths'].append(episode_length)

        # 提取任务完成率和平均延迟
        if eval_results['performance_metrics']:
            last_metrics = eval_results['performance_metrics'][-1]
            eval_results['task_completion_rates'].append(
                last_metrics.get('task_completion_rate', 0)
            )
            eval_results['average_delays'].append(
                last_metrics.get('total_delay', 0)
            )

    # 计算统计数据
    stats = {
        'mean_reward': np.mean(eval_results['episode_rewards']),
        'std_reward': np.std(eval_results['episode_rewards']),
        'mean_length': np.mean(eval_results['episode_lengths']),
        'mean_completion_rate': np.mean(eval_results['task_completion_rates']),
        'mean_delay': np.mean(eval_results['average_delays'])
    }

    return eval_results, stats


def train_simple_mappo(env, config_dict: Dict, save_dir: str) -> Dict:
    """训练简化MAPPO算法"""
    print("=== 开始简化MAPPO训练 ===")
    
    # 获取状态和动作空间信息
    state_dim = env.observation_space.shape[0]
    action_space = env.action_space
    
    # 创建简化MAPPO配置
    mappo_config = SimpleMAPPOConfig()
    mappo_config.total_timesteps = config_dict['ppo'].total_timesteps
    mappo_config.n_epochs = config_dict['ppo'].n_epochs
    mappo_config.learning_rate = config_dict['ppo'].learning_rate
    mappo_config.steps_per_update = config_dict['ppo'].steps_per_update
    
    # 创建多个简化MAPPO智能体
    agents = {
        'rsu': SimpleMAPPOAgent('rsu', state_dim, action_space, mappo_config),
        'uav': SimpleMAPPOAgent('uav', state_dim, action_space, mappo_config),
        'vehicle': SimpleMAPPOAgent('vehicle', state_dim, action_space, mappo_config)
    }
    
    # 创建简化MAPPO训练器
    trainer = SimpleMAPPOTrainer(env, agents, mappo_config)
    
    # 训练
    training_results = trainer.train(mappo_config.total_timesteps)
    
    # 保存模型
    for agent_id, agent in agents.items():
        model_path = f"{save_dir}/models/simple_mappo_{agent_id}_model.pt"
        agent.save(model_path)
    
    # 评估
    eval_results, eval_stats = evaluate_simple_mappo_agent(env, agents, num_episodes=20)
    
    return {
        'training_results': training_results,
        'eval_results': eval_results,
        'eval_stats': eval_stats,
        'algorithm': 'Simple MAPPO'
    }
