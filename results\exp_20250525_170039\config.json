{"system": {"delta_t": 0.1, "max_time_slots": 1000, "num_vehicles": 20, "num_rsus": 8, "num_uavs": 4, "area_width": 2000.0, "area_height": 2000.0}, "task": {"task_arrival_rate": 0.5, "data_size_range": [1000000.0, 10000000.0], "computation_density": 1000, "output_ratio": 0.1, "max_tolerance_range": [5, 20], "num_priorities": 3, "priority_weights": [0.3, 0.5, 0.2], "max_lifetime": 20}, "ppo": {"hidden_dim": 256, "num_layers": 3, "learning_rate": 0.0003, "lr_decay_steps": 100000, "lr_decay_rate": 0.8, "batch_size": 64, "n_epochs": 10, "update_epochs": 10, "clip_range": 0.2, "clip_epsilon": 0.2, "gamma": 0.99, "gae_lambda": 0.95, "steps_per_update": 2048, "entropy_coef": 0.01, "value_loss_coef": 0.5, "max_grad_norm": 0.5, "total_timesteps": 1000000, "eval_freq": 10000, "save_freq": 50000}}